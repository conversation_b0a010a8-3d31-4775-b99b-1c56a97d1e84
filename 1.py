import pyautogui
import win32gui
import win32con
import time
import logging
from uiautomation import WindowControl

# 设置日志记录
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def getHwnd():
    try:
        # 尝试多种可能的微信窗口类名
        possible_class_names = ['WeChatMainWndForPC', 'ChatWnd', 'WeChat']
        possible_window_names = ['微信', 'WeChat']

        hwnd = None
        for class_name in possible_class_names:
            for window_name in possible_window_names:
                hwnd = win32gui.FindWindow(class_name, window_name)
                if hwnd:
                    logging.info(f"找到微信窗口: 类名={class_name}, 窗口名={window_name}")
                    break
            if hwnd:
                break

        if not hwnd:
            # 如果找不到，尝试枚举所有窗口
            logging.info("尝试枚举所有窗口查找微信...")
            def enum_windows_callback(hwnd, windows):
                if win32gui.IsWindowVisible(hwnd):
                    window_text = win32gui.GetWindowText(hwnd)
                    class_name = win32gui.GetClassName(hwnd)
                    if '微信' in window_text or 'WeChat' in window_text:
                        windows.append((hwnd, window_text, class_name))
                return True

            windows = []
            win32gui.EnumWindows(enum_windows_callback, windows)

            if windows:
                logging.info(f"找到可能的微信窗口: {windows}")
                hwnd = windows[0][0]  # 使用第一个找到的窗口
            else:
                logging.error("未找到任何微信窗口，请确保微信已启动")
                return None

        if hwnd:
            win32gui.ShowWindow(hwnd, win32con.SW_SHOWNORMAL)
            win32gui.SetForegroundWindow(hwnd)
            logging.info("微信窗口已激活")

        return hwnd
    except Exception as e:
        logging.error(f"获取窗口句柄时出错: {e}")
        return None

def fuwei(hwnd):
    try:
        if hwnd:
            left, top, right, bottom = win32gui.GetWindowRect(hwnd)
            leftpoint = left + 155
            toppoint = top + 150
            pyautogui.moveTo(leftpoint, toppoint)
            pyautogui.click()
            logging.info('复位操作完成')
        else:
            logging.warning('窗口句柄无效，无法执行复位操作')
    except Exception as e:
        logging.error(f"执行复位时出错: {e}")

def main():
    hwnd = getHwnd()
    if hwnd is None:
        logging.error('无法找到微信窗口，脚本退出')
        return

    try:
        # 连接微信窗口
        wx = WindowControl(Name="微信")
        if not wx.Exists(0, 0):
            logging.error('无法连接到微信窗口控件')
            return

        wx.SwitchToThisWindow()
        logging.info('已切换到微信窗口')

        # 首先点击"微信"按钮确保在聊天界面
        try:
            wechat_button = wx.ButtonControl(Name="微信", searchDepth=6)
            if wechat_button.Exists(0, 0):
                wechat_button.Click()
                logging.info('已点击微信按钮，切换到聊天界面')
                time.sleep(1)
            else:
                logging.warning('未找到微信按钮')
        except Exception as e:
            logging.warning(f'点击微信按钮时出错: {e}')

        logging.info('开始监听微信消息...')

    except Exception as e:
        logging.error(f"初始化微信控件时出错: {e}")
        return

    # 简化的监听逻辑 - 使用键盘监听和剪贴板检测
    import pyperclip
    last_clipboard = ""

    while True:
        try:
            # 检查剪贴板是否有新内容（可能是复制的消息）
            current_clipboard = pyperclip.paste()

            # 简单的消息检测：检查是否有新的文本内容
            if current_clipboard != last_clipboard and current_clipboard.strip():
                # 检查是否是我们关心的关键词
                msg = current_clipboard.strip()
                logging.info(f'检测到剪贴板内容: {msg}')

                response = None
                if msg == '你好':
                    response = '你好，这条消息是通过autoreply程序进行自动回复！'
                elif msg == '123':
                    response = '你发送了123'
                elif msg == '测试':
                    response = '测试成功'

                if response:
                    logging.info(f'准备回复: {response}')
                    # 将回复内容放到剪贴板
                    pyperclip.copy(response)
                    # 模拟Ctrl+V粘贴
                    pyautogui.hotkey('ctrl', 'v')
                    time.sleep(0.5)
                    # 模拟回车发送
                    pyautogui.press('enter')
                    logging.info('已发送自动回复')
                    fuwei(hwnd)

                last_clipboard = current_clipboard

            time.sleep(1)  # 检查间隔

        except Exception as e:
            logging.error(f"监听过程中出错: {e}")
            time.sleep(3)

if __name__ == "__main__":
    main()

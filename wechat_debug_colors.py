import pyautogui
import win32gui
import win32con
import time
import logging
import pyperclip
from uiautomation import WindowControl
import os

# 设置日志记录
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def getHwnd():
    """获取微信窗口句柄"""
    try:
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_text = win32gui.GetWindowText(hwnd)
                if window_text == '微信':
                    windows.append(hwnd)
            return True
        
        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)
        
        if windows:
            hwnd = windows[0]
            try:
                win32gui.ShowWindow(hwnd, win32con.SW_SHOWNORMAL)
                time.sleep(0.5)
                win32gui.SetForegroundWindow(hwnd)
                logging.info("微信窗口已激活")
            except Exception as e:
                logging.warning(f"激活窗口警告: {e}")
            return hwnd
        else:
            logging.error("未找到微信窗口")
            return None
    except Exception as e:
        logging.error(f"获取窗口句柄出错: {e}")
        return None

def analyze_chat_list_colors(wx):
    """分析聊天列表区域的颜色分布"""
    try:
        rect = wx.BoundingRectangle
        left, top, right, bottom = rect.left, rect.top, rect.right, rect.bottom
        
        logging.info(f"微信窗口位置: 左={left}, 上={top}, 右={right}, 下={bottom}")
        
        # 聊天列表区域
        chat_list_left = left + 20
        chat_list_right = left + (right - left) // 3
        chat_list_top = top + 120
        chat_list_bottom = bottom - 100
        
        logging.info(f"聊天列表区域: 左={chat_list_left}, 右={chat_list_right}, 上={chat_list_top}, 下={chat_list_bottom}")
        
        # 分析颜色分布
        logging.info("开始分析聊天列表区域的颜色...")
        
        red_pixels = []
        orange_pixels = []
        bright_pixels = []
        
        # 扫描聊天列表区域
        for y in range(chat_list_top, chat_list_bottom, 20):  # 每20像素扫描一次
            for x in range(chat_list_left, chat_list_right, 20):
                try:
                    pixel_color = pyautogui.pixel(x, y)
                    r, g, b = pixel_color
                    
                    # 检测各种可能的未读标识颜色
                    if r > 200 and g < 100 and b < 100:  # 红色
                        red_pixels.append((x, y, r, g, b))
                    elif r > 200 and g > 100 and g < 200 and b < 100:  # 橙色
                        orange_pixels.append((x, y, r, g, b))
                    elif r > 180 and g > 180 and b > 180:  # 亮色（可能是白色数字）
                        bright_pixels.append((x, y, r, g, b))
                        
                except:
                    pass
        
        # 报告发现的颜色
        if red_pixels:
            logging.info(f"发现 {len(red_pixels)} 个红色像素:")
            for i, (x, y, r, g, b) in enumerate(red_pixels[:5]):  # 只显示前5个
                logging.info(f"  红色 {i+1}: 位置({x}, {y}), RGB({r}, {g}, {b})")
        
        if orange_pixels:
            logging.info(f"发现 {len(orange_pixels)} 个橙色像素:")
            for i, (x, y, r, g, b) in enumerate(orange_pixels[:5]):
                logging.info(f"  橙色 {i+1}: 位置({x}, {y}), RGB({r}, {g}, {b})")
        
        if bright_pixels:
            logging.info(f"发现 {len(bright_pixels)} 个亮色像素:")
            for i, (x, y, r, g, b) in enumerate(bright_pixels[:5]):
                logging.info(f"  亮色 {i+1}: 位置({x}, {y}), RGB({r}, {g}, {b})")
        
        if not red_pixels and not orange_pixels and not bright_pixels:
            logging.info("未发现明显的未读标识颜色")
        
        return red_pixels, orange_pixels, bright_pixels
        
    except Exception as e:
        logging.error(f"分析颜色时出错: {e}")
        return [], [], []

def take_screenshot_and_analyze(wx):
    """截图并分析聊天列表区域"""
    try:
        rect = wx.BoundingRectangle
        left, top, right, bottom = rect.left, rect.top, rect.right, rect.bottom
        
        # 聊天列表区域
        chat_list_left = left + 20
        chat_list_right = left + (right - left) // 3
        chat_list_top = top + 120
        chat_list_bottom = bottom - 100
        
        # 截取聊天列表区域的截图
        screenshot = pyautogui.screenshot(region=(chat_list_left, chat_list_top, 
                                                 chat_list_right - chat_list_left, 
                                                 chat_list_bottom - chat_list_top))
        
        # 保存截图
        screenshot_path = "chat_list_screenshot.png"
        screenshot.save(screenshot_path)
        logging.info(f"聊天列表截图已保存到: {screenshot_path}")
        
        return screenshot_path
        
    except Exception as e:
        logging.error(f"截图时出错: {e}")
        return None

def try_different_detection_methods(wx):
    """尝试不同的未读消息检测方法"""
    try:
        rect = wx.BoundingRectangle
        left, top, right, bottom = rect.left, rect.top, rect.right, rect.bottom
        
        chat_list_left = left + 20
        chat_list_right = left + (right - left) // 3
        chat_list_top = top + 120
        chat_list_bottom = bottom - 100
        
        logging.info("尝试不同的检测方法...")
        
        # 方法1: 检测红色圆点
        logging.info("方法1: 检测红色圆点")
        for y in range(chat_list_top, chat_list_bottom, 60):
            for x in range(chat_list_right - 100, chat_list_right - 10, 10):
                try:
                    pixel_color = pyautogui.pixel(x, y)
                    r, g, b = pixel_color
                    if r > 150 and g < 100 and b < 100:  # 降低红色阈值
                        logging.info(f"方法1发现可能的红色标识: 位置({x}, {y}), RGB({r}, {g}, {b})")
                except:
                    pass
        
        # 方法2: 检测数字标识（白色文字在红色背景上）
        logging.info("方法2: 检测数字标识")
        for y in range(chat_list_top, chat_list_bottom, 60):
            for x in range(chat_list_right - 100, chat_list_right - 10, 5):
                try:
                    pixel_color = pyautogui.pixel(x, y)
                    r, g, b = pixel_color
                    # 检测白色文字
                    if r > 200 and g > 200 and b > 200:
                        # 检查周围是否有红色背景
                        for dx in [-2, -1, 0, 1, 2]:
                            for dy in [-2, -1, 0, 1, 2]:
                                try:
                                    bg_color = pyautogui.pixel(x + dx, y + dy)
                                    bg_r, bg_g, bg_b = bg_color
                                    if bg_r > 150 and bg_g < 100 and bg_b < 100:
                                        logging.info(f"方法2发现可能的数字标识: 位置({x}, {y}), 白色文字RGB({r}, {g}, {b}), 红色背景RGB({bg_r}, {bg_g}, {bg_b})")
                                        return True
                                except:
                                    pass
                except:
                    pass
        
        # 方法3: 使用UI控件检测
        logging.info("方法3: 使用UI控件检测")
        try:
            # 递归查找所有控件
            def find_unread_controls(control, depth=0, max_depth=5):
                if depth > max_depth:
                    return []
                
                found = []
                try:
                    children = control.GetChildren()
                    for child in children:
                        try:
                            # 查找可能包含未读数字的文本控件
                            if (child.ControlTypeName == 'TextControl' and 
                                child.Name and child.Name.isdigit() and 
                                int(child.Name) > 0):
                                found.append(child)
                                logging.info(f"方法3发现未读数字控件: {child.Name}")
                        except:
                            pass
                        
                        # 递归查找
                        found.extend(find_unread_controls(child, depth + 1, max_depth))
                except:
                    pass
                
                return found
            
            unread_controls = find_unread_controls(wx)
            if unread_controls:
                logging.info(f"方法3找到 {len(unread_controls)} 个未读控件")
                return True
        except Exception as e:
            logging.error(f"方法3出错: {e}")
        
        return False
        
    except Exception as e:
        logging.error(f"检测方法出错: {e}")
        return False

def main():
    """主函数"""
    logging.info('=== 微信未读消息检测调试程序 ===')
    
    # 获取微信窗口
    hwnd = getHwnd()
    if hwnd is None:
        logging.error('无法找到微信窗口，程序退出')
        return

    try:
        # 连接微信窗口
        wx = WindowControl(Name="微信")
        if not wx.Exists(0, 0):
            logging.error('无法连接到微信窗口控件')
            return
            
        wx.SwitchToThisWindow()
        logging.info('已切换到微信窗口')
        
        # 确保在微信聊天界面
        try:
            wechat_button = wx.ButtonControl(Name="微信", searchDepth=6)
            if wechat_button.Exists(0, 0):
                wechat_button.Click()
                logging.info('已点击微信按钮，切换到聊天界面')
                time.sleep(2)
        except Exception as e:
            logging.warning(f'点击微信按钮时出错: {e}')

        logging.info('开始调试分析...')
        
        # 1. 截图分析
        screenshot_path = take_screenshot_and_analyze(wx)
        
        # 2. 颜色分析
        red_pixels, orange_pixels, bright_pixels = analyze_chat_list_colors(wx)
        
        # 3. 尝试不同检测方法
        detection_result = try_different_detection_methods(wx)
        
        # 4. 总结报告
        logging.info("\n=== 调试分析报告 ===")
        logging.info(f"截图保存: {'成功' if screenshot_path else '失败'}")
        logging.info(f"红色像素数量: {len(red_pixels)}")
        logging.info(f"橙色像素数量: {len(orange_pixels)}")
        logging.info(f"亮色像素数量: {len(bright_pixels)}")
        logging.info(f"检测方法结果: {'发现未读标识' if detection_result else '未发现未读标识'}")
        
        if screenshot_path:
            logging.info(f"请查看截图文件: {os.path.abspath(screenshot_path)}")
        
        logging.info("调试分析完成")
        
    except Exception as e:
        logging.error(f"程序运行时出错: {e}")

if __name__ == "__main__":
    main()

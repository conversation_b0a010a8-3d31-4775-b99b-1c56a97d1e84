import pyautogui
import win32gui
import win32con
import time
import logging
import pyperclip
from uiautomation import WindowControl
import re

# 设置日志记录
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def getHwnd():
    """获取微信窗口句柄"""
    try:
        logging.info("查找微信窗口...")
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_text = win32gui.GetWindowText(hwnd)
                if window_text == '微信':
                    windows.append(hwnd)
            return True
        
        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)
        
        if windows:
            hwnd = windows[0]
            logging.info("找到微信窗口")
            try:
                win32gui.ShowWindow(hwnd, win32con.SW_SHOWNORMAL)
                time.sleep(0.5)
                win32gui.SetForegroundWindow(hwnd)
                logging.info("微信窗口已激活")
            except Exception as e:
                logging.warning(f"激活窗口警告: {e}")
            return hwnd
        else:
            logging.error("未找到微信窗口")
            return None
    except Exception as e:
        logging.error(f"获取窗口句柄出错: {e}")
        return None

def find_unread_chats_by_coordinates(wx):
    """通过坐标和颜色检测未读聊天"""
    try:
        # 获取微信窗口的位置和大小
        rect = wx.BoundingRectangle
        left, top, right, bottom = rect.left, rect.top, rect.right, rect.bottom
        
        # 聊天列表通常在左侧，大约占窗口宽度的1/3
        chat_list_left = left + 10
        chat_list_right = left + (right - left) // 3
        chat_list_top = top + 100  # 跳过标题栏
        chat_list_bottom = bottom - 50
        
        unread_chats = []
        
        # 在聊天列表区域查找红色圆点（未读标识）
        for y in range(chat_list_top, chat_list_bottom, 60):  # 每60像素检查一次（大约一个聊天项的高度）
            for x in range(chat_list_left, chat_list_right, 20):
                try:
                    # 获取像素颜色
                    pixel_color = pyautogui.pixel(x, y)
                    
                    # 检查是否是红色（未读标识通常是红色）
                    r, g, b = pixel_color
                    if r > 200 and g < 100 and b < 100:  # 红色像素
                        # 找到可能的未读标识，记录这个位置
                        chat_y = y
                        chat_x = chat_list_left + 50  # 聊天项的点击位置
                        unread_chats.append((chat_x, chat_y))
                        logging.info(f"发现未读聊天标识在位置: ({chat_x}, {chat_y})")
                        break
                except:
                    pass
        
        return unread_chats
    except Exception as e:
        logging.error(f"查找未读聊天出错: {e}")
        return []

def click_chat_by_coordinates(x, y):
    """通过坐标点击聊天"""
    try:
        pyautogui.click(x, y)
        logging.info(f"点击聊天位置: ({x}, {y})")
        time.sleep(1)
        return True
    except Exception as e:
        logging.error(f"点击聊天出错: {e}")
        return False

def get_latest_message_by_selection():
    """通过选择文本获取最新消息"""
    try:
        # 使用Ctrl+A选择所有文本，然后Ctrl+C复制
        pyautogui.hotkey('ctrl', 'a')
        time.sleep(0.3)
        pyautogui.hotkey('ctrl', 'c')
        time.sleep(0.3)
        
        # 获取剪贴板内容
        content = pyperclip.paste()
        
        if content:
            # 解析聊天记录，提取最新消息
            lines = content.split('\n')
            messages = []
            
            for line in lines:
                line = line.strip()
                if line and not line.startswith('[') and len(line) < 100:
                    # 过滤掉时间戳和系统消息
                    if not re.match(r'^\d{2}:\d{2}', line) and not line.startswith('——'):
                        messages.append(line)
            
            # 返回最后几条消息
            if messages:
                latest_messages = messages[-3:] if len(messages) >= 3 else messages
                logging.info(f"获取到最新消息: {latest_messages}")
                return latest_messages
        
        return []
    except Exception as e:
        logging.error(f"获取消息出错: {e}")
        return []

def get_auto_reply(message):
    """根据消息内容生成自动回复"""
    message = message.strip()
    
    if '你好' in message:
        return '你好，这条消息是通过autoreply程序进行自动回复！'
    elif '123' in message:
        return '你发送了123'
    elif '测试' in message:
        return '测试成功'
    elif 'help' in message or '帮助' in message:
        return '可用命令：你好、123、测试'
    elif '?' in message or '？' in message:
        return '有什么可以帮助您的吗？'
    else:
        return '自动回复：收到您的消息了~'

def send_reply(message):
    """发送回复消息"""
    try:
        # 点击输入框（通常在窗口底部）
        time.sleep(0.5)
        
        # 输入消息
        pyperclip.copy(message)
        pyautogui.hotkey('ctrl', 'v')
        time.sleep(0.3)
        
        # 发送消息
        pyautogui.press('enter')
        time.sleep(0.5)
        
        logging.info(f'已发送回复: {message}')
        return True
    except Exception as e:
        logging.error(f'发送回复出错: {e}')
        return False

def main():
    """主函数"""
    logging.info('=== 微信智能自动回复程序启动 ===')
    
    # 获取微信窗口
    hwnd = getHwnd()
    if hwnd is None:
        logging.error('无法找到微信窗口，程序退出')
        return

    try:
        # 连接微信窗口
        wx = WindowControl(Name="微信")
        if not wx.Exists(0, 0):
            logging.error('无法连接到微信窗口控件')
            return
            
        wx.SwitchToThisWindow()
        logging.info('已切换到微信窗口')
        
        # 确保在微信聊天界面
        try:
            wechat_button = wx.ButtonControl(Name="微信", searchDepth=6)
            if wechat_button.Exists(0, 0):
                wechat_button.Click()
                logging.info('已点击微信按钮，切换到聊天界面')
                time.sleep(1)
        except Exception as e:
            logging.warning(f'点击微信按钮时出错: {e}')

        logging.info('开始智能监听未读消息...')
        logging.info('程序将自动检测未读聊天并进行回复')
        
        processed_positions = set()  # 记录已处理的位置
        
        while True:
            try:
                # 查找未读聊天
                unread_chats = find_unread_chats_by_coordinates(wx)
                
                for chat_x, chat_y in unread_chats:
                    position_key = f"{chat_x}_{chat_y}"
                    
                    if position_key not in processed_positions:
                        logging.info(f'发现新的未读聊天，准备点击...')
                        
                        # 点击聊天
                        if click_chat_by_coordinates(chat_x, chat_y):
                            time.sleep(1)
                            
                            # 获取最新消息
                            messages = get_latest_message_by_selection()
                            
                            if messages:
                                # 对最新消息进行回复
                                latest_message = messages[-1]
                                response = get_auto_reply(latest_message)
                                
                                logging.info(f'收到消息: {latest_message}')
                                logging.info(f'准备回复: {response}')
                                
                                # 发送回复
                                if send_reply(response):
                                    processed_positions.add(position_key)
                                    logging.info('回复发送成功')
                            else:
                                logging.info('未能获取到消息内容')
                
                # 清理处理记录
                if len(processed_positions) > 50:
                    processed_positions.clear()
                
                time.sleep(5)  # 检查间隔
                
            except KeyboardInterrupt:
                logging.info('用户中断程序')
                break
            except Exception as e:
                logging.error(f"监听过程中出错: {e}")
                time.sleep(5)
                
    except Exception as e:
        logging.error(f"程序运行时出错: {e}")
    
    logging.info('=== 微信智能自动回复程序结束 ===')

if __name__ == "__main__":
    main()

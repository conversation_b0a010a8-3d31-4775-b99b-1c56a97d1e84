import pyautogui
import win32gui
import win32con
import time
import logging
import pyperclip
from uiautomation import WindowControl, Control

# 设置日志记录
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def getHwnd():
    """获取微信窗口句柄"""
    try:
        logging.info("尝试枚举所有窗口查找微信...")
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_text = win32gui.GetWindowText(hwnd)
                class_name = win32gui.GetClassName(hwnd)
                if '微信' in window_text or 'WeChat' in window_text:
                    windows.append((hwnd, window_text, class_name))
            return True
        
        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)
        
        if windows:
            logging.info(f"找到可能的微信窗口: {windows}")
            # 选择最匹配的窗口（窗口名称正好是"微信"的）
            for window_hwnd, window_text, class_name in windows:
                if window_text == '微信':
                    hwnd = window_hwnd
                    logging.info(f"选择微信窗口: {window_text}")
                    break
            else:
                hwnd = windows[0][0]  # 如果没有找到完全匹配的，使用第一个
        else:
            logging.error("未找到任何微信窗口，请确保微信已启动")
            return None
        
        if hwnd:
            try:
                win32gui.ShowWindow(hwnd, win32con.SW_SHOWNORMAL)
                time.sleep(0.5)
                win32gui.SetForegroundWindow(hwnd)
                logging.info("微信窗口已激活")
            except Exception as e:
                logging.warning(f"激活窗口时出现警告: {e}，但继续执行")
        
        return hwnd
    except Exception as e:
        logging.error(f"获取窗口句柄时出错: {e}")
        return None

def find_unread_chats(wx):
    """查找未读聊天"""
    try:
        unread_chats = []
        
        # 递归查找所有控件
        def find_controls_recursive(control, depth=0, max_depth=6):
            if depth > max_depth:
                return []
            
            found_controls = []
            try:
                # 查找带有红点标识的聊天项
                # 通常未读消息会有特殊的控件结构或属性
                children = control.GetChildren()
                for child in children:
                    try:
                        # 检查是否是聊天列表项
                        if (child.ControlTypeName in ['ListItemControl', 'GroupControl', 'CustomControl'] and 
                            child.Name and len(child.Name.strip()) > 0):
                            
                            # 检查是否有子控件包含数字（未读消息数）
                            sub_children = child.GetChildren()
                            has_unread_indicator = False
                            
                            for sub_child in sub_children:
                                try:
                                    if (sub_child.ControlTypeName == 'TextControl' and 
                                        sub_child.Name and sub_child.Name.isdigit()):
                                        has_unread_indicator = True
                                        break
                                except:
                                    pass
                            
                            if has_unread_indicator:
                                found_controls.append(child)
                                logging.info(f"找到未读聊天: {child.Name}")
                    except:
                        pass
                    
                    # 递归查找
                    found_controls.extend(find_controls_recursive(child, depth + 1, max_depth))
                        
            except Exception as e:
                pass
            
            return found_controls
        
        unread_chats = find_controls_recursive(wx)
        return unread_chats
        
    except Exception as e:
        logging.error(f"查找未读聊天时出错: {e}")
        return []

def click_chat_and_get_message(chat_control, wx):
    """点击聊天并获取最新消息"""
    try:
        # 点击聊天项
        chat_control.Click()
        logging.info(f"已点击聊天: {chat_control.Name}")
        time.sleep(1)  # 等待聊天窗口加载
        
        # 尝试获取最新消息
        messages = get_latest_messages(wx)
        return messages
        
    except Exception as e:
        logging.error(f"点击聊天时出错: {e}")
        return []

def get_latest_messages(wx):
    """获取当前聊天窗口的最新消息"""
    try:
        messages = []
        
        # 递归查找消息控件
        def find_message_controls(control, depth=0, max_depth=6):
            if depth > max_depth:
                return []
            
            found_messages = []
            try:
                children = control.GetChildren()
                for child in children:
                    try:
                        # 查找可能的消息控件
                        if (child.ControlTypeName in ['TextControl', 'EditControl', 'DocumentControl'] and 
                            child.Name and len(child.Name.strip()) > 0 and
                            len(child.Name.strip()) < 200):  # 限制消息长度
                            
                            # 过滤掉一些明显不是消息的文本
                            text = child.Name.strip()
                            if (not text.startswith('http') and 
                                not text in ['发送', '表情', '文件', '截图', '语音', '视频'] and
                                not text.isdigit() and
                                len(text) > 1):
                                found_messages.append(text)
                    except:
                        pass
                    
                    # 递归查找
                    found_messages.extend(find_message_controls(child, depth + 1, max_depth))
                        
            except Exception as e:
                pass
            
            return found_messages
        
        messages = find_message_controls(wx)
        
        # 去重并返回最后几条消息
        unique_messages = []
        for msg in reversed(messages):  # 从后往前遍历，获取最新的
            if msg not in unique_messages:
                unique_messages.append(msg)
            if len(unique_messages) >= 3:  # 最多返回3条最新消息
                break
        
        return list(reversed(unique_messages))  # 恢复时间顺序
        
    except Exception as e:
        logging.error(f"获取消息时出错: {e}")
        return []

def get_auto_reply(message):
    """根据消息内容生成自动回复"""
    message = message.strip()
    
    if message == '你好':
        return '你好，这条消息是通过autoreply程序进行自动回复！'
    elif message == '123':
        return '你发送了123'
    elif message == '测试':
        return '测试成功'
    elif message == 'help' or message == '帮助':
        return '可用命令：你好、123、测试'
    else:
        return '自动回复：无法匹配该关键词~'

def send_message(message):
    """发送消息到微信"""
    try:
        # 将消息复制到剪贴板
        pyperclip.copy(message)
        time.sleep(0.2)
        
        # 粘贴消息
        pyautogui.hotkey('ctrl', 'v')
        time.sleep(0.3)
        
        # 发送消息
        pyautogui.press('enter')
        time.sleep(0.2)
        
        logging.info(f'已发送消息: {message}')
        return True
    except Exception as e:
        logging.error(f'发送消息时出错: {e}')
        return False

def main():
    """主函数"""
    logging.info('=== 微信未读消息自动回复程序启动 ===')
    
    # 获取微信窗口
    hwnd = getHwnd()
    if hwnd is None:
        logging.error('无法找到微信窗口，程序退出')
        return

    try:
        # 连接微信窗口
        wx = WindowControl(Name="微信")
        if not wx.Exists(0, 0):
            logging.error('无法连接到微信窗口控件')
            return
            
        wx.SwitchToThisWindow()
        logging.info('已切换到微信窗口')
        
        # 确保在微信聊天界面
        try:
            wechat_button = wx.ButtonControl(Name="微信", searchDepth=6)
            if wechat_button.Exists(0, 0):
                wechat_button.Click()
                logging.info('已点击微信按钮，切换到聊天界面')
                time.sleep(1)
        except Exception as e:
            logging.warning(f'点击微信按钮时出错: {e}')

        logging.info('开始监听未读消息...')
        processed_chats = set()  # 记录已处理的聊天
        
        while True:
            try:
                # 查找未读聊天
                unread_chats = find_unread_chats(wx)
                
                for chat in unread_chats:
                    chat_name = chat.Name
                    if chat_name not in processed_chats:
                        logging.info(f'发现新的未读聊天: {chat_name}')
                        
                        # 点击聊天并获取消息
                        messages = click_chat_and_get_message(chat, wx)
                        
                        if messages:
                            logging.info(f'获取到消息: {messages}')
                            
                            # 对最新消息进行回复
                            latest_message = messages[-1] if messages else ""
                            if latest_message:
                                response = get_auto_reply(latest_message)
                                logging.info(f'准备回复: {response}')
                                
                                # 发送回复
                                if send_message(response):
                                    processed_chats.add(chat_name)
                        else:
                            logging.info(f'未能获取到 {chat_name} 的消息')
                
                # 清理处理记录，避免内存占用过多
                if len(processed_chats) > 100:
                    processed_chats.clear()
                
                time.sleep(3)  # 检查间隔
                
            except KeyboardInterrupt:
                logging.info('用户中断程序')
                break
            except Exception as e:
                logging.error(f"监听过程中出错: {e}")
                time.sleep(5)
                
    except Exception as e:
        logging.error(f"程序运行时出错: {e}")
    
    logging.info('=== 微信未读消息自动回复程序结束 ===')

if __name__ == "__main__":
    main()

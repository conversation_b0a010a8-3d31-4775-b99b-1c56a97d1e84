#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信消息监测程序 V2.0
专门针对现代微信版本优化，支持多窗口监测
"""

import time
import win32gui
import win32con
import win32clipboard
import win32process
import psutil
from datetime import datetime
import re
import os
import threading

class WeChatMonitorV2:
    def __init__(self):
        """初始化监测器"""
        self.monitored_windows = {}  # 存储所有监测的窗口
        self.is_monitoring = False
        self.message_count = 0
        self.log_file = "wechat_messages_v2.log"
        self.config = {
            "check_interval": 1.0,  # 检查间隔（秒）
            "log_to_file": True,    # 是否记录到文件
            "monitor_all_windows": True,  # 监测所有微信窗口
            "show_window_details": True   # 显示窗口详细信息
        }
        
        # 创建日志文件
        if self.config["log_to_file"]:
            self.init_log_file()
            
    def init_log_file(self):
        """初始化日志文件"""
        try:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(f"\n{'='*80}\n")
                f.write(f"微信监测开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"{'='*80}\n")
        except Exception as e:
            print(f"初始化日志文件失败: {e}")
            
    def log_message(self, message):
        """记录消息到文件和控制台"""
        print(message)
        if self.config["log_to_file"]:
            try:
                with open(self.log_file, 'a', encoding='utf-8') as f:
                    f.write(f"{message}\n")
            except Exception as e:
                print(f"写入日志失败: {e}")
                
    def find_all_wechat_windows(self):
        """查找所有微信窗口"""
        # 先找微信进程
        wechat_pids = []
        for proc in psutil.process_iter(['pid', 'name', 'exe']):
            try:
                if proc.info['name'] and 'WeChat' in proc.info['name']:
                    wechat_pids.append(proc.info['pid'])
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        if not wechat_pids:
            return []
            
        # 查找所有微信相关窗口
        windows = []
        
        def enum_windows_callback(hwnd, windows_list):
            if win32gui.IsWindowVisible(hwnd):
                try:
                    class_name = win32gui.GetClassName(hwnd)
                    window_text = win32gui.GetWindowText(hwnd)
                    _, window_pid = win32process.GetWindowThreadProcessId(hwnd)
                    
                    # 检查是否属于微信进程
                    if window_pid in wechat_pids:
                        # 获取窗口大小
                        rect = win32gui.GetWindowRect(hwnd)
                        width = rect[2] - rect[0]
                        height = rect[3] - rect[1]
                        
                        # 过滤掉太小的窗口（可能是子控件）
                        if width > 50 and height > 50:
                            windows_list.append({
                                'hwnd': hwnd,
                                'title': window_text,
                                'class_name': class_name,
                                'pid': window_pid,
                                'rect': rect,
                                'width': width,
                                'height': height,
                                'area': width * height,
                                'is_foreground': win32gui.GetForegroundWindow() == hwnd
                            })
                            
                except Exception as e:
                    pass
                    
        win32gui.EnumWindows(enum_windows_callback, windows)
        
        # 按窗口面积排序，大窗口优先
        windows.sort(key=lambda w: w['area'], reverse=True)
        
        return windows
        
    def classify_window(self, window):
        """分类窗口类型"""
        title = window['title']
        class_name = window['class_name']
        
        if "WeChatMainWndForPC" in class_name:
            return "main_window"
        elif title == "微信":
            return "tray_window"
        elif len(title) > 10 and any(char.isdigit() for char in title):
            return "chat_window"  # 可能是聊天窗口
        elif len(title) > 0:
            return "other_window"
        else:
            return "unknown"
            
    def start_monitoring(self):
        """开始监测"""
        self.log_message("🚀 微信消息监测程序 V2.0 启动")
        self.log_message("=" * 60)
        
        self.is_monitoring = True
        
        while self.is_monitoring:
            try:
                # 查找所有微信窗口
                windows = self.find_all_wechat_windows()
                
                if not windows:
                    self.log_message("❌ 未找到微信窗口，5秒后重试...")
                    time.sleep(5)
                    continue
                
                # 首次运行时显示所有窗口信息
                if not self.monitored_windows:
                    self.log_message(f"🔍 找到 {len(windows)} 个微信窗口:")
                    for i, window in enumerate(windows, 1):
                        window_type = self.classify_window(window)
                        self.log_message(f"  窗口 {i}: '{window['title']}'")
                        self.log_message(f"    类型: {window_type}")
                        self.log_message(f"    类名: {window['class_name']}")
                        self.log_message(f"    大小: {window['width']}x{window['height']}")
                        self.log_message(f"    前台: {window['is_foreground']}")
                        self.log_message("")
                
                # 监测每个窗口的标题变化
                for window in windows:
                    hwnd = window['hwnd']
                    current_title = window['title']
                    
                    # 检查标题是否发生变化
                    if hwnd in self.monitored_windows:
                        last_title = self.monitored_windows[hwnd]['last_title']
                        if current_title != last_title:
                            self.handle_title_change(window, last_title, current_title)
                    
                    # 更新窗口信息
                    self.monitored_windows[hwnd] = {
                        'last_title': current_title,
                        'window_info': window,
                        'last_update': datetime.now()
                    }
                
                # 清理已关闭的窗口
                closed_windows = []
                for hwnd in self.monitored_windows:
                    if not win32gui.IsWindow(hwnd):
                        closed_windows.append(hwnd)
                
                for hwnd in closed_windows:
                    window_info = self.monitored_windows[hwnd]['window_info']
                    self.log_message(f"🔴 窗口已关闭: '{window_info['title']}'")
                    del self.monitored_windows[hwnd]
                
                time.sleep(self.config["check_interval"])
                
            except KeyboardInterrupt:
                self.log_message("\n⏹️ 用户停止监测")
                break
            except Exception as e:
                self.log_message(f"❌ 监测过程中出错: {e}")
                time.sleep(3)
        
        self.is_monitoring = False
        self.log_message(f"📊 监测结束，共检测到 {self.message_count} 条消息变化")
        
    def handle_title_change(self, window, old_title, new_title):
        """处理标题变化"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        window_type = self.classify_window(window)
        
        # 分析变化类型
        if self.is_new_message_indication(old_title, new_title):
            self.message_count += 1
            self.log_message(f"[{timestamp}] 🔔 新消息检测!")
            self.log_message(f"  窗口: '{new_title}'")
            self.log_message(f"  类型: {window_type}")
            if old_title:
                self.log_message(f"  变化: '{old_title}' → '{new_title}'")
        else:
            self.log_message(f"[{timestamp}] 📱 窗口标题变化")
            self.log_message(f"  窗口: '{new_title}'")
            self.log_message(f"  类型: {window_type}")
            if old_title:
                self.log_message(f"  变化: '{old_title}' → '{new_title}'")
        
        self.log_message("")
        
    def is_new_message_indication(self, old_title, new_title):
        """判断是否是新消息提示"""
        # 检查标题中是否出现了消息计数
        old_has_count = bool(re.search(r'\(\d+\)', old_title)) if old_title else False
        new_has_count = bool(re.search(r'\(\d+\)', new_title))
        
        # 如果新标题有计数而旧标题没有，或者计数增加了
        if new_has_count and not old_has_count:
            return True
            
        if old_has_count and new_has_count:
            old_count = int(re.search(r'\((\d+)\)', old_title).group(1))
            new_count = int(re.search(r'\((\d+)\)', new_title).group(1))
            return new_count > old_count
            
        return False
        
    def stop_monitoring(self):
        """停止监测"""
        self.is_monitoring = False

def show_banner():
    """显示程序横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════╗
    ║               微信消息监测程序 V2.0                       ║
    ║              WeChat Message Monitor V2.0                ║
    ╠══════════════════════════════════════════════════════════╣
    ║  新功能特性：                                            ║
    ║  • 智能识别所有微信窗口类型                              ║
    ║  • 多窗口同时监测                                        ║
    ║  • 更准确的新消息检测算法                                ║
    ║  • 详细的窗口分类和状态显示                              ║
    ║  • 自动清理已关闭窗口                                    ║
    ║  • 优化的日志记录格式                                    ║
    ╚══════════════════════════════════════════════════════════╝
    """
    print(banner)

def main():
    """主函数"""
    show_banner()
    
    # 创建监测器实例
    monitor = WeChatMonitorV2()
    
    print("正在初始化监测器...")
    print(f"日志文件: {monitor.log_file}")
    print(f"检查间隔: {monitor.config['check_interval']}秒")
    print()
    
    try:
        # 开始监测
        monitor.start_monitoring()
    except KeyboardInterrupt:
        monitor.log_message("\n用户手动停止程序")
        print("程序已安全退出")
    except Exception as e:
        error_msg = f"程序运行出错: {e}"
        monitor.log_message(error_msg)
        print(error_msg)
    finally:
        # 记录程序结束时间
        if hasattr(monitor, 'log_message'):
            monitor.log_message(f"程序结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            monitor.log_message("=" * 80)

if __name__ == "__main__":
    main()

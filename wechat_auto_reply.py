import pyautogui
import win32gui
import win32con
import time
import logging
import pyperclip
from uiautomation import WindowControl

# 设置日志记录
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def getHwnd():
    """获取微信窗口句柄"""
    try:
        # 尝试多种可能的微信窗口类名
        possible_class_names = ['WeChatMainWndForPC', 'ChatWnd', 'WeChat']
        possible_window_names = ['微信', 'WeChat']
        
        hwnd = None
        for class_name in possible_class_names:
            for window_name in possible_window_names:
                hwnd = win32gui.FindWindow(class_name, window_name)
                if hwnd:
                    logging.info(f"找到微信窗口: 类名={class_name}, 窗口名={window_name}")
                    break
            if hwnd:
                break
        
        if not hwnd:
            # 如果找不到，尝试枚举所有窗口
            logging.info("尝试枚举所有窗口查找微信...")
            def enum_windows_callback(hwnd, windows):
                if win32gui.IsWindowVisible(hwnd):
                    window_text = win32gui.GetWindowText(hwnd)
                    class_name = win32gui.GetClassName(hwnd)
                    if '微信' in window_text or 'WeChat' in window_text:
                        windows.append((hwnd, window_text, class_name))
                return True
            
            windows = []
            win32gui.EnumWindows(enum_windows_callback, windows)
            
            if windows:
                logging.info(f"找到可能的微信窗口: {windows}")
                # 选择最匹配的窗口（窗口名称正好是"微信"的）
                for window_hwnd, window_text, class_name in windows:
                    if window_text == '微信':
                        hwnd = window_hwnd
                        logging.info(f"选择微信窗口: {window_text}")
                        break
                else:
                    hwnd = windows[0][0]  # 如果没有找到完全匹配的，使用第一个
            else:
                logging.error("未找到任何微信窗口，请确保微信已启动")
                return None
        
        if hwnd:
            try:
                win32gui.ShowWindow(hwnd, win32con.SW_SHOWNORMAL)
                time.sleep(0.5)  # 等待窗口显示
                win32gui.SetForegroundWindow(hwnd)
                logging.info("微信窗口已激活")
            except Exception as e:
                logging.warning(f"激活窗口时出现警告: {e}，但继续执行")
        
        return hwnd
    except Exception as e:
        logging.error(f"获取窗口句柄时出错: {e}")
        return None

def fuwei(hwnd):
    """复位操作 - 点击微信窗口的特定位置"""
    try:
        if hwnd:
            left, top, right, bottom = win32gui.GetWindowRect(hwnd)
            leftpoint = left + 155
            toppoint = top + 150
            pyautogui.moveTo(leftpoint, toppoint)
            pyautogui.click()
            logging.info('复位操作完成')
        else:
            logging.warning('窗口句柄无效，无法执行复位操作')
    except Exception as e:
        logging.error(f"执行复位时出错: {e}")

def get_auto_reply(message):
    """根据消息内容生成自动回复"""
    message = message.strip()
    
    if message == '你好':
        return '你好，这条消息是通过autoreply程序进行自动回复！'
    elif message == '123':
        return '你发送了123'
    elif message == '测试':
        return '测试成功'
    elif message == 'help' or message == '帮助':
        return '可用命令：你好、123、测试'
    else:
        return '自动回复：无法匹配该关键词~'

def send_message(message):
    """发送消息到微信"""
    try:
        # 将消息复制到剪贴板
        pyperclip.copy(message)
        time.sleep(0.2)
        
        # 粘贴消息
        pyautogui.hotkey('ctrl', 'v')
        time.sleep(0.3)
        
        # 发送消息
        pyautogui.press('enter')
        time.sleep(0.2)
        
        logging.info(f'已发送消息: {message}')
        return True
    except Exception as e:
        logging.error(f'发送消息时出错: {e}')
        return False

def main():
    """主函数"""
    logging.info('=== 微信自动回复程序启动 ===')
    
    # 获取微信窗口
    hwnd = getHwnd()
    if hwnd is None:
        logging.error('无法找到微信窗口，程序退出')
        return

    try:
        # 连接微信窗口
        wx = WindowControl(Name="微信")
        if not wx.Exists(0, 0):
            logging.error('无法连接到微信窗口控件')
            return
            
        wx.SwitchToThisWindow()
        logging.info('已切换到微信窗口')
        
        # 尝试点击微信按钮确保在聊天界面
        try:
            wechat_button = wx.ButtonControl(Name="微信", searchDepth=6)
            if wechat_button.Exists(0, 0):
                wechat_button.Click()
                logging.info('已点击微信按钮，切换到聊天界面')
                time.sleep(1)
        except Exception as e:
            logging.warning(f'点击微信按钮时出错: {e}')

        logging.info('开始监听微信消息...')
        logging.info('使用说明：')
        logging.info('1. 在微信中选择一个聊天窗口')
        logging.info('2. 复制收到的消息（Ctrl+C）')
        logging.info('3. 程序会自动检测并回复')
        logging.info('4. 支持的关键词：你好、123、测试、help/帮助')
        
        # 监听剪贴板变化
        last_clipboard = ""
        message_history = set()  # 防止重复回复同一条消息
        
        while True:
            try:
                # 检查剪贴板内容
                current_clipboard = pyperclip.paste()
                
                if (current_clipboard != last_clipboard and 
                    current_clipboard.strip() and 
                    current_clipboard not in message_history and
                    len(current_clipboard.strip()) < 100):  # 限制消息长度
                    
                    msg = current_clipboard.strip()
                    logging.info(f'检测到新消息: {msg}')
                    
                    # 生成回复
                    response = get_auto_reply(msg)
                    
                    if response:
                        logging.info(f'准备回复: {response}')
                        
                        # 发送回复
                        if send_message(response):
                            message_history.add(current_clipboard)
                            fuwei(hwnd)
                        
                        # 限制历史记录大小
                        if len(message_history) > 50:
                            message_history.clear()
                    
                    last_clipboard = current_clipboard
                
                time.sleep(1)  # 检查间隔
                
            except KeyboardInterrupt:
                logging.info('用户中断程序')
                break
            except Exception as e:
                logging.error(f"监听过程中出错: {e}")
                time.sleep(3)
                
    except Exception as e:
        logging.error(f"程序运行时出错: {e}")
    
    logging.info('=== 微信自动回复程序结束 ===')

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信消息监测程序 - 简化版
直接监测所有包含特定关键词的窗口
"""

import time
import win32gui
import win32process
from datetime import datetime
import re

class SimpleWeChatMonitor:
    def __init__(self):
        """初始化监测器"""
        self.monitored_windows = {}
        self.is_monitoring = False
        self.message_count = 0
        self.log_file = "wechat_simple.log"
        
        # 初始化日志
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(f"\n{'='*60}\n")
            f.write(f"监测开始: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"{'='*60}\n")
            
    def log_message(self, message):
        """记录消息"""
        print(message)
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(f"{message}\n")
            
    def find_target_windows(self):
        """查找目标窗口"""
        windows = []
        
        def enum_windows_callback(hwnd, windows_list):
            if win32gui.IsWindowVisible(hwnd):
                try:
                    window_text = win32gui.GetWindowText(hwnd)
                    class_name = win32gui.GetClassName(hwnd)
                    
                    # 查找包含数字的窗口标题（可能是聊天窗口）
                    # 或者包含"微信"的窗口
                    if (window_text and 
                        (any(char.isdigit() for char in window_text) or 
                         "微信" in window_text or
                         "WeChat" in window_text) and
                        len(window_text.strip()) > 0):
                        
                        # 排除明显不是微信的窗口
                        if ("Visual Studio" not in window_text and
                            "Code" not in window_text and
                            "Chrome" not in window_text and
                            "Firefox" not in window_text):
                            
                            windows_list.append({
                                'hwnd': hwnd,
                                'title': window_text,
                                'class_name': class_name
                            })
                            
                except Exception as e:
                    pass
                    
        win32gui.EnumWindows(enum_windows_callback, windows)
        return windows
        
    def is_message_change(self, old_title, new_title):
        """判断是否是消息变化"""
        if not old_title or not new_title:
            return False
            
        # 检查是否有新的数字出现（可能是消息计数）
        old_numbers = set(re.findall(r'\d+', old_title))
        new_numbers = set(re.findall(r'\d+', new_title))
        
        # 检查括号中的数字变化（消息计数）
        old_count = re.search(r'\((\d+)\)', old_title)
        new_count = re.search(r'\((\d+)\)', new_title)
        
        if new_count and not old_count:
            return True  # 新出现了消息计数
            
        if old_count and new_count:
            old_num = int(old_count.group(1))
            new_num = int(new_count.group(1))
            return new_num > old_num  # 消息计数增加
            
        # 检查标题长度变化（可能是新联系人）
        if len(new_title) != len(old_title):
            return True
            
        return False
        
    def start_monitoring(self):
        """开始监测"""
        self.log_message("🚀 开始监测微信窗口...")
        self.log_message("=" * 50)
        
        self.is_monitoring = True
        first_run = True
        
        while self.is_monitoring:
            try:
                windows = self.find_target_windows()
                
                if not windows:
                    if first_run:
                        self.log_message("❌ 未找到目标窗口，继续监测...")
                        first_run = False
                    time.sleep(2)
                    continue
                
                # 首次运行显示找到的窗口
                if first_run:
                    self.log_message(f"✅ 找到 {len(windows)} 个可能的微信窗口:")
                    for i, window in enumerate(windows, 1):
                        self.log_message(f"  {i}. '{window['title']}'")
                        self.log_message(f"     类名: {window['class_name']}")
                    self.log_message("")
                    first_run = False
                
                # 监测每个窗口的变化
                for window in windows:
                    hwnd = window['hwnd']
                    current_title = window['title']
                    
                    if hwnd in self.monitored_windows:
                        last_title = self.monitored_windows[hwnd]
                        
                        if current_title != last_title:
                            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                            
                            if self.is_message_change(last_title, current_title):
                                self.message_count += 1
                                self.log_message(f"[{timestamp}] 🔔 检测到消息变化!")
                                self.log_message(f"  窗口: '{current_title}'")
                                self.log_message(f"  变化: '{last_title}' → '{current_title}'")
                                self.log_message(f"  总计: {self.message_count} 次变化")
                            else:
                                self.log_message(f"[{timestamp}] 📱 窗口标题变化")
                                self.log_message(f"  窗口: '{current_title}'")
                                self.log_message(f"  变化: '{last_title}' → '{current_title}'")
                            
                            self.log_message("")
                    
                    # 更新记录
                    self.monitored_windows[hwnd] = current_title
                
                # 清理已关闭的窗口
                valid_hwnds = {w['hwnd'] for w in windows}
                closed_hwnds = set(self.monitored_windows.keys()) - valid_hwnds
                for hwnd in closed_hwnds:
                    del self.monitored_windows[hwnd]
                
                time.sleep(1)  # 每秒检查一次
                
            except KeyboardInterrupt:
                self.log_message("\n⏹️ 用户停止监测")
                break
            except Exception as e:
                self.log_message(f"❌ 错误: {e}")
                time.sleep(3)
        
        self.is_monitoring = False
        self.log_message(f"📊 监测结束，共检测到 {self.message_count} 次消息变化")

def main():
    """主函数"""
    print("=" * 60)
    print("           微信消息监测程序 - 简化版")
    print("=" * 60)
    print("功能：监测所有可能的微信窗口标题变化")
    print("特点：无需精确识别，广泛监测")
    print("操作：按 Ctrl+C 停止监测")
    print("=" * 60)
    print()
    
    monitor = SimpleWeChatMonitor()
    
    try:
        monitor.start_monitoring()
    except KeyboardInterrupt:
        print("\n程序已退出")
    except Exception as e:
        print(f"程序出错: {e}")

if __name__ == "__main__":
    main()

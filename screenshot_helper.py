import pyautogui
import time
import logging
import tkinter as tk
from tkinter import messagebox
import os

# 设置日志记录
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class ScreenshotHelper:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("微信未读标识截图工具")
        self.root.geometry("400x300")
        
        # 创建界面
        self.create_widgets()
        
    def create_widgets(self):
        """创建界面组件"""
        # 标题
        title_label = tk.Label(self.root, text="微信未读标识截图工具", font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # 说明文字
        instruction_text = """
使用说明：
1. 点击下面的按钮开始截图
2. 程序会给您3秒准备时间
3. 然后您可以拖拽选择未读标识区域
4. 截图会自动保存为 unread_X.png

建议截图内容：
- 1条未读消息的红色圆圈
- 2条未读消息的红色圆圈
- 3-10条未读消息的红色圆圈
- 尽量只截取红色圆圈部分，不要包含太多背景
        """
        
        instruction_label = tk.Label(self.root, text=instruction_text, justify=tk.LEFT, wraplength=350)
        instruction_label.pack(pady=10)
        
        # 截图按钮
        self.screenshot_button = tk.Button(self.root, text="开始截图未读标识", 
                                         command=self.start_screenshot, 
                                         font=("Arial", 12), 
                                         bg="#4CAF50", fg="white",
                                         width=20, height=2)
        self.screenshot_button.pack(pady=10)
        
        # 状态标签
        self.status_label = tk.Label(self.root, text="准备就绪", fg="green")
        self.status_label.pack(pady=5)
        
        # 已保存的截图列表
        self.update_saved_list()
        
    def update_saved_list(self):
        """更新已保存的截图列表"""
        saved_files = []
        for i in range(1, 11):
            filename = f"unread_{i}.png"
            if os.path.exists(filename):
                saved_files.append(filename)
        
        if hasattr(self, 'saved_label'):
            self.saved_label.destroy()
        
        if saved_files:
            saved_text = "已保存的截图:\n" + "\n".join(saved_files)
        else:
            saved_text = "还没有保存任何截图"
        
        self.saved_label = tk.Label(self.root, text=saved_text, fg="blue")
        self.saved_label.pack(pady=5)
        
    def start_screenshot(self):
        """开始截图流程"""
        # 询问截图编号
        number = self.ask_screenshot_number()
        if number is None:
            return
        
        filename = f"unread_{number}.png"
        
        # 检查文件是否已存在
        if os.path.exists(filename):
            if not messagebox.askyesno("文件已存在", f"{filename} 已存在，是否覆盖？"):
                return
        
        # 最小化窗口
        self.root.iconify()
        
        # 倒计时
        for i in range(3, 0, -1):
            print(f"截图倒计时: {i} 秒...")
            time.sleep(1)
        
        try:
            # 进行截图
            print("请拖拽选择未读标识区域...")
            screenshot = pyautogui.screenshot()
            
            # 让用户选择区域（这里使用简单的方法，实际可以改进）
            # 由于pyautogui的限制，我们使用全屏截图然后提示用户手动裁剪
            screenshot.save(f"temp_full_screenshot.png")
            
            messagebox.showinfo("截图完成", 
                               f"全屏截图已保存为 temp_full_screenshot.png\n"
                               f"请使用图片编辑工具裁剪出未读标识部分，\n"
                               f"然后保存为 {filename}")
            
        except Exception as e:
            messagebox.showerror("截图失败", f"截图时出错: {e}")
        
        # 恢复窗口
        self.root.deiconify()
        self.update_saved_list()
        
    def ask_screenshot_number(self):
        """询问截图编号"""
        dialog = tk.Toplevel(self.root)
        dialog.title("选择未读消息数量")
        dialog.geometry("300x200")
        dialog.transient(self.root)
        dialog.grab_set()
        
        result = [None]
        
        tk.Label(dialog, text="这个截图是几条未读消息？", font=("Arial", 12)).pack(pady=20)
        
        # 创建数字按钮
        button_frame = tk.Frame(dialog)
        button_frame.pack(pady=10)
        
        for i in range(1, 11):
            btn = tk.Button(button_frame, text=str(i), width=3, height=2,
                          command=lambda x=i: self.set_number(result, x, dialog))
            btn.grid(row=(i-1)//5, column=(i-1)%5, padx=2, pady=2)
        
        # 取消按钮
        cancel_btn = tk.Button(dialog, text="取消", command=dialog.destroy)
        cancel_btn.pack(pady=10)
        
        # 等待对话框关闭
        self.root.wait_window(dialog)
        return result[0]
    
    def set_number(self, result, number, dialog):
        """设置选择的数字"""
        result[0] = number
        dialog.destroy()
    
    def run(self):
        """运行程序"""
        self.root.mainloop()

def main():
    """主函数"""
    print("启动微信未读标识截图工具...")
    
    app = ScreenshotHelper()
    app.run()

if __name__ == "__main__":
    main()

import pyautogui
import win32gui
import win32con
import time
import logging
from uiautomation import WindowControl, Control

# 设置日志记录
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def getHwnd():
    try:
        # 尝试多种可能的微信窗口类名
        possible_class_names = ['WeChatMainWndForPC', 'ChatWnd', 'WeChat']
        possible_window_names = ['微信', 'WeChat']
        
        hwnd = None
        for class_name in possible_class_names:
            for window_name in possible_window_names:
                hwnd = win32gui.FindWindow(class_name, window_name)
                if hwnd:
                    logging.info(f"找到微信窗口: 类名={class_name}, 窗口名={window_name}")
                    break
            if hwnd:
                break
        
        if not hwnd:
            # 如果找不到，尝试枚举所有窗口
            logging.info("尝试枚举所有窗口查找微信...")
            def enum_windows_callback(hwnd, windows):
                if win32gui.IsWindowVisible(hwnd):
                    window_text = win32gui.GetWindowText(hwnd)
                    class_name = win32gui.GetClassName(hwnd)
                    if '微信' in window_text or 'WeChat' in window_text:
                        windows.append((hwnd, window_text, class_name))
                return True
            
            windows = []
            win32gui.EnumWindows(enum_windows_callback, windows)
            
            if windows:
                logging.info(f"找到可能的微信窗口: {windows}")
                # 选择最匹配的窗口（窗口名称正好是"微信"的）
                for window_hwnd, window_text, class_name in windows:
                    if window_text == '微信':
                        hwnd = window_hwnd
                        logging.info(f"选择微信窗口: {window_text}")
                        break
                else:
                    hwnd = windows[0][0]  # 如果没有找到完全匹配的，使用第一个
            else:
                logging.error("未找到任何微信窗口，请确保微信已启动")
                return None
        
        if hwnd:
            try:
                win32gui.ShowWindow(hwnd, win32con.SW_SHOWNORMAL)
                time.sleep(0.5)  # 等待窗口显示
                win32gui.SetForegroundWindow(hwnd)
                logging.info("微信窗口已激活")
            except Exception as e:
                logging.warning(f"激活窗口时出现警告: {e}，但继续执行")
                # 即使激活失败，也继续执行
        
        return hwnd
    except Exception as e:
        logging.error(f"获取窗口句柄时出错: {e}")
        return None

def analyze_wechat_structure():
    """分析微信窗口结构"""
    hwnd = getHwnd()
    if hwnd is None:
        logging.error('无法找到微信窗口')
        return

    try:
        # 尝试多种方式连接到微信窗口
        wx = None

        # 方法1：通过窗口名称
        try:
            wx = WindowControl(Name="微信")
            if wx.Exists(0, 0):
                logging.info('通过窗口名称连接到微信')
            else:
                wx = None
        except:
            wx = None

        # 方法2：通过句柄
        if wx is None:
            try:
                wx = WindowControl(Handle=hwnd)
                if wx.Exists(0, 0):
                    logging.info('通过句柄连接到微信')
                else:
                    wx = None
            except:
                wx = None

        # 方法3：通过类名
        if wx is None:
            try:
                wx = WindowControl(ClassName="Qt51514QWindowIcon")
                if wx.Exists(0, 0):
                    logging.info('通过类名连接到微信')
                else:
                    wx = None
            except:
                wx = None

        if wx is None:
            logging.error('无法连接到微信窗口控件')
            return

        wx.SwitchToThisWindow()
        logging.info('已切换到微信窗口')
        
        # 递归打印控件结构
        def print_control_tree(control, depth=0, max_depth=4):
            if depth > max_depth:
                return

            indent = "  " * depth
            try:
                name = control.Name if control.Name else "<无名称>"
                control_type = control.ControlTypeName
                logging.info(f"{indent}{control_type}: {name}")

                # 获取子控件
                children = control.GetChildren()
                for child in children[:8]:  # 增加显示的子控件数量
                    print_control_tree(child, depth + 1, max_depth)

            except Exception as e:
                logging.error(f"{indent}获取控件信息时出错: {e}")
        
        logging.info("=== 微信窗口控件结构 ===")
        print_control_tree(wx)
        
        # 尝试查找常见的控件
        logging.info("\n=== 查找常见控件 ===")

        # 递归查找所有控件类型
        def find_controls_recursive(control, target_types, depth=0, max_depth=5):
            if depth > max_depth:
                return []

            found_controls = []
            try:
                if control.ControlTypeName in target_types:
                    found_controls.append((control, depth))

                children = control.GetChildren()
                for child in children:
                    found_controls.extend(find_controls_recursive(child, target_types, depth + 1, max_depth))
            except:
                pass

            return found_controls

        # 查找列表控件
        try:
            list_controls = find_controls_recursive(wx, ['ListControl'])
            for control, depth in list_controls:
                logging.info(f"找到列表控件 (深度{depth}): {control.Name}")
        except Exception as e:
            logging.error(f"查找列表控件时出错: {e}")

        # 查找编辑框
        try:
            edit_controls = find_controls_recursive(wx, ['EditControl'])
            for control, depth in edit_controls:
                logging.info(f"找到编辑框 (深度{depth}): {control.Name}")
        except Exception as e:
            logging.error(f"查找编辑框时出错: {e}")

        # 查找文本控件
        try:
            text_controls = find_controls_recursive(wx, ['TextControl'])
            for control, depth in text_controls[:10]:  # 限制显示数量
                logging.info(f"找到文本控件 (深度{depth}): {control.Name}")
        except Exception as e:
            logging.error(f"查找文本控件时出错: {e}")

        # 查找按钮控件
        try:
            button_controls = find_controls_recursive(wx, ['ButtonControl'])
            for control, depth in button_controls[:10]:  # 限制显示数量
                logging.info(f"找到按钮控件 (深度{depth}): {control.Name}")
        except Exception as e:
            logging.error(f"查找按钮控件时出错: {e}")
            
    except Exception as e:
        logging.error(f"分析微信结构时出错: {e}")

if __name__ == "__main__":
    analyze_wechat_structure()

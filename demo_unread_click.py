import pyautogui
import win32gui
import win32con
import time
import logging
import pyperclip
from uiautomation import WindowControl

# 设置日志记录
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def getHwnd():
    """获取微信窗口句柄"""
    try:
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_text = win32gui.GetWindowText(hwnd)
                if window_text == '微信':
                    windows.append(hwnd)
            return True
        
        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)
        
        if windows:
            hwnd = windows[0]
            win32gui.ShowWindow(hwnd, win32con.SW_SHOWNORMAL)
            time.sleep(0.5)
            win32gui.SetForegroundWindow(hwnd)
            logging.info("微信窗口已激活")
            return hwnd
        else:
            logging.error("未找到微信窗口")
            return None
    except Exception as e:
        logging.error(f"获取窗口句柄出错: {e}")
        return None

def demo_click_unread():
    """演示点击未读聊天的功能"""
    logging.info('=== 微信未读聊天点击演示 ===')
    
    # 获取微信窗口
    hwnd = getHwnd()
    if hwnd is None:
        return

    try:
        # 连接微信窗口
        wx = WindowControl(Name="微信")
        if not wx.Exists(0, 0):
            logging.error('无法连接到微信窗口控件')
            return
            
        wx.SwitchToThisWindow()
        logging.info('已切换到微信窗口')
        
        # 获取微信窗口的位置信息
        rect = wx.BoundingRectangle
        left, top, right, bottom = rect.left, rect.top, rect.right, rect.bottom
        logging.info(f'微信窗口位置: 左={left}, 上={top}, 右={right}, 下={bottom}')
        
        # 计算聊天列表区域（通常在左侧1/3）
        chat_list_left = left + 20
        chat_list_right = left + (right - left) // 3
        chat_list_top = top + 120  # 跳过标题栏和搜索框
        chat_list_bottom = bottom - 100
        
        logging.info(f'聊天列表区域: 左={chat_list_left}, 右={chat_list_right}, 上={chat_list_top}, 下={chat_list_bottom}')
        
        # 演示：在聊天列表区域查找并点击
        logging.info('开始扫描聊天列表区域...')
        
        # 方法1：按行扫描聊天列表
        chat_item_height = 60  # 每个聊天项大约60像素高
        for i in range(0, 10):  # 检查前10个聊天项
            chat_y = chat_list_top + (i * chat_item_height) + 30  # 聊天项中心位置
            chat_x = chat_list_left + 60  # 聊天项点击位置
            
            if chat_y > chat_list_bottom:
                break
                
            logging.info(f'检查聊天项 {i+1}: 位置({chat_x}, {chat_y})')
            
            # 检查这个位置是否有红色未读标识
            try:
                # 在聊天项右侧查找红色圆点
                for check_x in range(chat_list_right - 50, chat_list_right - 10, 5):
                    pixel_color = pyautogui.pixel(check_x, chat_y)
                    r, g, b = pixel_color
                    
                    # 检查是否是红色（未读标识）
                    if r > 180 and g < 80 and b < 80:
                        logging.info(f'发现未读标识在位置: ({check_x}, {chat_y})')
                        logging.info(f'准备点击聊天项: ({chat_x}, {chat_y})')
                        
                        # 点击聊天项
                        pyautogui.click(chat_x, chat_y)
                        time.sleep(1)
                        
                        # 尝试获取聊天内容
                        logging.info('尝试获取聊天内容...')
                        get_chat_content()
                        
                        return True
            except Exception as e:
                logging.warning(f'检查像素颜色时出错: {e}')
        
        logging.info('未发现未读聊天')
        
        # 方法2：演示手动点击指定位置
        logging.info('\n=== 演示手动点击功能 ===')
        logging.info('将在3秒后点击聊天列表的第一个项目...')
        time.sleep(3)
        
        first_chat_x = chat_list_left + 60
        first_chat_y = chat_list_top + 30
        
        logging.info(f'点击第一个聊天项: ({first_chat_x}, {first_chat_y})')
        pyautogui.click(first_chat_x, first_chat_y)
        time.sleep(1)
        
        # 获取聊天内容
        get_chat_content()
        
    except Exception as e:
        logging.error(f"演示过程中出错: {e}")

def get_chat_content():
    """获取当前聊天窗口的内容"""
    try:
        logging.info('获取聊天内容...')
        
        # 方法1：使用Ctrl+A选择所有内容
        pyautogui.hotkey('ctrl', 'a')
        time.sleep(0.5)
        pyautogui.hotkey('ctrl', 'c')
        time.sleep(0.5)
        
        content = pyperclip.paste()
        
        if content:
            lines = content.split('\n')
            # 过滤并显示最后几条消息
            messages = []
            for line in lines[-10:]:  # 只看最后10行
                line = line.strip()
                if line and len(line) < 100 and not line.startswith('['):
                    messages.append(line)
            
            if messages:
                logging.info('获取到的最新消息:')
                for i, msg in enumerate(messages[-5:], 1):  # 显示最后5条
                    logging.info(f'  {i}. {msg}')
                
                # 演示自动回复
                if messages:
                    last_message = messages[-1]
                    reply = generate_reply(last_message)
                    logging.info(f'生成的回复: {reply}')
                    
                    # 发送回复（演示）
                    send_demo_reply(reply)
            else:
                logging.info('未获取到有效消息')
        else:
            logging.info('剪贴板为空')
            
    except Exception as e:
        logging.error(f'获取聊天内容时出错: {e}')

def generate_reply(message):
    """生成回复消息"""
    message = message.lower().strip()
    
    if '你好' in message or 'hello' in message:
        return '你好！很高兴收到您的消息。'
    elif '测试' in message or 'test' in message:
        return '测试成功！程序运行正常。'
    elif '123' in message:
        return '收到数字123'
    elif '?' in message or '？' in message:
        return '有什么可以帮助您的吗？'
    else:
        return '自动回复：已收到您的消息。'

def send_demo_reply(reply):
    """发送演示回复"""
    try:
        logging.info(f'准备发送回复: {reply}')
        
        # 将回复复制到剪贴板
        pyperclip.copy(reply)
        time.sleep(0.3)
        
        # 粘贴到输入框
        pyautogui.hotkey('ctrl', 'v')
        time.sleep(0.5)
        
        # 发送消息
        pyautogui.press('enter')
        time.sleep(0.3)
        
        logging.info('回复已发送')
        
    except Exception as e:
        logging.error(f'发送回复时出错: {e}')

if __name__ == "__main__":
    demo_click_unread()

import pyautogui
import win32gui
import win32con
import time
import logging
import pyperclip
from uiautomation import WindowControl
import os
import cv2
import numpy as np

# 设置日志记录
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 图像识别的置信度阈值
CONFIDENCE_THRESHOLD = 0.95

def getHwnd():
    """获取微信窗口句柄"""
    try:
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_text = win32gui.GetWindowText(hwnd)
                if window_text == '微信':
                    windows.append(hwnd)
            return True
        
        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)
        
        if windows:
            hwnd = windows[0]
            try:
                win32gui.ShowWindow(hwnd, win32con.SW_SHOWNORMAL)
                time.sleep(0.5)
                win32gui.SetForegroundWindow(hwnd)
                logging.info("微信窗口已激活")
            except Exception as e:
                logging.warning(f"激活窗口警告: {e}")
            return hwnd
        else:
            logging.error("未找到微信窗口")
            return None
    except Exception as e:
        logging.error(f"获取窗口句柄出错: {e}")
        return None

def load_unread_templates():
    """加载未读标识模板图片"""
    templates = []
    template_dir = "."  # 当前目录
    
    # 查找所有可能的未读标识图片
    template_files = []
    for i in range(1, 11):  # 1-10条未读
        for ext in ['.png', '.jpg', '.jpeg']:
            filename = f"unread_{i}{ext}"
            if os.path.exists(filename):
                template_files.append(filename)
    
    # 也查找其他可能的命名方式
    for filename in os.listdir(template_dir):
        if (filename.lower().startswith('unread') or 
            filename.lower().startswith('未读') or
            '未读' in filename) and filename.lower().endswith(('.png', '.jpg', '.jpeg')):
            if filename not in template_files:
                template_files.append(filename)
    
    for template_file in template_files:
        try:
            template = cv2.imread(template_file, cv2.IMREAD_COLOR)
            if template is not None:
                templates.append((template_file, template))
                logging.info(f"加载模板图片: {template_file}")
            else:
                logging.warning(f"无法加载图片: {template_file}")
        except Exception as e:
            logging.error(f"加载模板图片 {template_file} 时出错: {e}")
    
    if not templates:
        logging.warning("未找到任何未读标识模板图片")
        logging.info("请将未读标识截图保存为以下格式:")
        logging.info("  unread_1.png (1条未读)")
        logging.info("  unread_2.png (2条未读)")
        logging.info("  ... 等等")
    
    return templates

def find_unread_by_image(wx, templates):
    """使用图像识别查找未读标识"""
    try:
        if not templates:
            return []
        
        # 获取微信窗口区域
        rect = wx.BoundingRectangle
        left, top, right, bottom = rect.left, rect.top, rect.right, rect.bottom
        
        # 聊天列表区域
        chat_list_left = left + 20
        chat_list_right = left + (right - left) // 3
        chat_list_top = top + 120
        chat_list_bottom = bottom - 100
        
        # 截取聊天列表区域
        screenshot = pyautogui.screenshot(region=(chat_list_left, chat_list_top, 
                                                 chat_list_right - chat_list_left, 
                                                 chat_list_bottom - chat_list_top))
        
        # 转换为OpenCV格式
        screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        
        found_matches = []
        
        # 对每个模板进行匹配
        for template_name, template in templates:
            try:
                # 模板匹配
                result = cv2.matchTemplate(screenshot_cv, template, cv2.TM_CCOEFF_NORMED)
                locations = np.where(result >= CONFIDENCE_THRESHOLD)
                
                for pt in zip(*locations[::-1]):  # 切换x和y坐标
                    # 计算在屏幕上的绝对坐标
                    screen_x = chat_list_left + pt[0] + template.shape[1] // 2
                    screen_y = chat_list_top + pt[1] + template.shape[0] // 2
                    
                    # 计算点击位置（聊天项的中心）
                    click_x = chat_list_left + 150  # 聊天项的点击位置
                    click_y = screen_y
                    
                    confidence = result[pt[1], pt[0]]
                    
                    found_matches.append({
                        'template': template_name,
                        'confidence': confidence,
                        'screen_pos': (screen_x, screen_y),
                        'click_pos': (click_x, click_y)
                    })
                    
                    logging.info(f"发现未读标识! 模板:{template_name}, 置信度:{confidence:.2f}, 位置:({screen_x}, {screen_y})")
                    
            except Exception as e:
                logging.error(f"匹配模板 {template_name} 时出错: {e}")
        
        # 按置信度排序，返回最匹配的结果
        found_matches.sort(key=lambda x: x['confidence'], reverse=True)
        
        return found_matches
        
    except Exception as e:
        logging.error(f"图像识别时出错: {e}")
        return []

def click_chat_and_get_message(x, y):
    """点击聊天并获取消息"""
    try:
        logging.info(f"点击聊天位置: ({x}, {y})")

        # 确保微信窗口在前台
        hwnd = getHwnd()
        if hwnd:
            try:
                win32gui.SetForegroundWindow(hwnd)
                time.sleep(0.5)
            except:
                pass

        # 点击聊天项
        pyautogui.click(x, y)
        time.sleep(1.5)

        # 再次确保微信窗口聚焦
        if hwnd:
            try:
                win32gui.SetForegroundWindow(hwnd)
                time.sleep(0.5)
            except:
                pass

        # 点击聊天区域中心确保聚焦到聊天内容区域
        chat_area_x = x + 300  # 聊天区域大概位置
        chat_area_y = y + 50   # 稍微向下一点
        pyautogui.click(chat_area_x, chat_area_y)
        time.sleep(1)

        # 再次确保微信窗口聚焦
        if hwnd:
            try:
                win32gui.SetForegroundWindow(hwnd)
                time.sleep(0.5)
            except:
                pass

        # 先滚动到最底部确保看到最新消息
        pyautogui.press('end')
        time.sleep(0.5)

        # 清空剪贴板
        pyperclip.copy("")
        time.sleep(0.3)

        # 获取聊天内容 - 多次尝试
        for attempt in range(3):
            logging.info(f"尝试获取聊天内容 - 第{attempt + 1}次")

            # 确保微信窗口聚焦
            if hwnd:
                try:
                    win32gui.SetForegroundWindow(hwnd)
                    time.sleep(0.3)
                except:
                    pass

            # 点击聊天区域确保聚焦
            pyautogui.click(chat_area_x, chat_area_y)
            time.sleep(0.5)

            # 选择所有内容
            pyautogui.hotkey('ctrl', 'a')
            time.sleep(0.8)
            pyautogui.hotkey('ctrl', 'c')
            time.sleep(0.8)

            content = pyperclip.paste()

            logging.info(f"第{attempt + 1}次尝试 - 剪贴板内容长度: {len(content) if content else 0}")
            if content and len(content) > 10:
                # 检查是否是代码内容（包含Python关键字）
                if not any(keyword in content for keyword in ['def ', 'import ', 'try:', 'logging.info', 'pyautogui']):
                    logging.info(f"获取到有效聊天内容，前100字符: {repr(content[:100])}")
                    return parse_received_messages(content)
                else:
                    logging.warning(f"第{attempt + 1}次获取到的是代码内容，重试...")
            else:
                logging.warning(f"第{attempt + 1}次未获取到有效内容")

            time.sleep(1)

        logging.warning("多次尝试后仍未能获取到聊天内容")
        return None

    except Exception as e:
        logging.error(f"点击聊天或获取消息时出错: {e}")
        return None

def parse_received_messages(content):
    """解析聊天内容，提取对方发送的消息"""
    try:
        lines = content.split('\n')

        # 记录所有行用于调试
        logging.info("聊天记录原始内容:")
        for i, line in enumerate(lines):
            logging.info(f"  {i}: {repr(line)}")

        # 识别对方消息的策略
        received_messages = []
        my_messages = []

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 跳过时间戳、系统消息等
            if (line.startswith('[') or
                line.startswith('——') or
                line.endswith('撤回了一条消息') or
                line.startswith('http') or
                '年' in line and '月' in line and '日' in line):
                continue

            # 识别我发送的消息（绿色背景的自动回复）
            if ('自动回复' in line or
                line.startswith('自动回复') or
                '已收到您的消息' in line or
                '谢谢' in line and len(line) < 20):
                my_messages.append(line)
                continue

            # 其他有效消息认为是对方发送的
            if (len(line) >= 1 and len(line) < 200 and
                not line.isspace()):
                received_messages.append(line)

        # 调试信息
        logging.info(f"识别到的我的消息: {my_messages}")
        logging.info(f"识别到的对方消息: {received_messages}")

        if received_messages:
            # 如果有多条对方消息，返回最新的几条
            if len(received_messages) > 1:
                # 返回最后3条对方消息
                recent_received = received_messages[-3:]
                logging.info(f"对方最近发送的消息: {recent_received}")
                return recent_received
            else:
                latest_received = received_messages[-1]
                logging.info(f"对方发送的最新消息: {latest_received}")
                return [latest_received]

        logging.warning("未识别到对方发送的消息")
        return None

    except Exception as e:
        logging.error(f"解析消息时出错: {e}")
        return None

def print_received_messages(messages):
    """打印收到的消息"""
    if isinstance(messages, list):
        print("=" * 60)
        print(f"检测到 {len(messages)} 条对方发送的消息:")
        for i, msg in enumerate(messages, 1):
            print(f"  {i}. {msg}")
        print("=" * 60)
        logging.info(f"收到 {len(messages)} 条消息: {messages}")
    else:
        print("=" * 60)
        print(f"收到新消息: {messages}")
        print("=" * 60)
        logging.info(f"收到消息: {messages}")

def main():
    """主函数"""
    logging.info('=== 微信图像识别消息监听程序启动 ===')
    
    # 检查OpenCV是否可用
    try:
        cv2.__version__
        logging.info(f"OpenCV版本: {cv2.__version__}")
    except:
        logging.error("OpenCV未安装，请运行: pip install opencv-python")
        return
    
    # 加载未读标识模板
    templates = load_unread_templates()
    if not templates:
        logging.error("未找到未读标识模板图片，程序退出")
        logging.info("请将未读标识截图保存到当前目录，命名为:")
        logging.info("  unread_1.png, unread_2.png, unread_3.png 等")
        return
    
    # 获取微信窗口
    hwnd = getHwnd()
    if hwnd is None:
        logging.error('无法找到微信窗口，程序退出')
        return

    try:
        # 连接微信窗口
        wx = WindowControl(Name="微信")
        if not wx.Exists(0, 0):
            logging.error('无法连接到微信窗口控件')
            return
            
        wx.SwitchToThisWindow()
        logging.info('已切换到微信窗口')
        
        # 确保在微信聊天界面
        try:
            wechat_button = wx.ButtonControl(Name="微信", searchDepth=6)
            if wechat_button.Exists(0, 0):
                wechat_button.Click()
                logging.info('已点击微信按钮，切换到聊天界面')
                time.sleep(2)
        except Exception as e:
            logging.warning(f'点击微信按钮时出错: {e}')

        logging.info('开始图像识别监听未读消息...')
        logging.info(f'已加载 {len(templates)} 个未读标识模板')
        logging.info(f'图像匹配置信度阈值: {CONFIDENCE_THRESHOLD}')
        logging.info('程序将只输出收到的消息，不进行自动回复')
        
        processed_positions = set()  # 记录已处理的位置
        
        while True:
            try:
                # 使用图像识别查找未读标识
                matches = find_unread_by_image(wx, templates)
                
                if matches:
                    logging.info(f"发现 {len(matches)} 个未读标识匹配")
                
                for match in matches:
                    click_x, click_y = match['click_pos']
                    position_key = f"{click_x}_{click_y}"
                    
                    if position_key not in processed_positions:
                        logging.info(f"处理未读消息 - 模板:{match['template']}, 置信度:{match['confidence']:.2f}")
                        
                        # 点击聊天并获取消息
                        latest_message = click_chat_and_get_message(click_x, click_y)
                        
                        if latest_message:
                            # 打印收到的消息
                            print_received_messages(latest_message)
                            processed_positions.add(position_key)
                        else:
                            logging.warning("未能获取到消息内容")
                
                # 清理处理记录
                if len(processed_positions) > 50:
                    processed_positions.clear()
                
                time.sleep(3)  # 检查间隔
                
            except KeyboardInterrupt:
                logging.info('用户中断程序')
                break
            except Exception as e:
                logging.error(f"监听过程中出错: {e}")
                time.sleep(5)
                
    except Exception as e:
        logging.error(f"程序运行时出错: {e}")
    
    logging.info('=== 微信图像识别消息监听程序结束 ===')

if __name__ == "__main__":
    main()

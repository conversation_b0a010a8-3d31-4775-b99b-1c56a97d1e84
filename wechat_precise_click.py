import pyautogui
import win32gui
import win32con
import time
import logging
import pyperclip
from uiautomation import WindowControl

# 设置日志记录
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 可调整的参数
CLICK_OFFSET_X = 150  # 点击位置相对于聊天列表左边的偏移量（可调整）
CLICK_OFFSET_Y = 0    # 点击位置相对于检测到的Y坐标的偏移量（可调整）

def getHwnd():
    """获取微信窗口句柄"""
    try:
        logging.info("查找微信窗口...")
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_text = win32gui.GetWindowText(hwnd)
                if window_text == '微信':
                    windows.append(hwnd)
            return True
        
        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)
        
        if windows:
            hwnd = windows[0]
            logging.info("找到微信窗口")
            try:
                win32gui.ShowWindow(hwnd, win32con.SW_SHOWNORMAL)
                time.sleep(0.5)
                win32gui.SetForegroundWindow(hwnd)
                logging.info("微信窗口已激活")
            except Exception as e:
                logging.warning(f"激活窗口警告: {e}")
            return hwnd
        else:
            logging.error("未找到微信窗口")
            return None
    except Exception as e:
        logging.error(f"获取窗口句柄出错: {e}")
        return None

def find_unread_chats_precise(wx):
    """精确查找未读聊天"""
    try:
        # 获取微信窗口的位置和大小
        rect = wx.BoundingRectangle
        left, top, right, bottom = rect.left, rect.top, rect.right, rect.bottom
        
        logging.info(f"微信窗口位置: 左={left}, 上={top}, 右={right}, 下={bottom}")
        
        # 聊天列表通常在左侧，大约占窗口宽度的1/3
        chat_list_left = left + 20
        chat_list_right = left + (right - left) // 3
        chat_list_top = top + 120  # 跳过标题栏
        chat_list_bottom = bottom - 100
        
        logging.info(f"聊天列表区域: 左={chat_list_left}, 右={chat_list_right}, 上={chat_list_top}, 下={chat_list_bottom}")
        
        unread_chats = []
        
        # 在聊天列表区域查找红色圆点（未读标识）
        for y in range(chat_list_top, chat_list_bottom, 60):  # 每60像素检查一次
            for x in range(chat_list_right - 80, chat_list_right - 10, 10):  # 在右侧区域查找红点
                try:
                    # 获取像素颜色
                    pixel_color = pyautogui.pixel(x, y)
                    
                    # 检查是否是红色（未读标识通常是红色）
                    r, g, b = pixel_color
                    if r > 180 and g < 80 and b < 80:  # 红色像素
                        # 找到可能的未读标识
                        chat_y = y + CLICK_OFFSET_Y
                        # 计算点击位置：在聊天项的合适位置
                        chat_x = chat_list_left + CLICK_OFFSET_X
                        
                        unread_chats.append((chat_x, chat_y))
                        logging.info(f"发现未读聊天标识在位置: ({x}, {y})")
                        logging.info(f"计算的点击位置: ({chat_x}, {chat_y})")
                        break
                except:
                    pass
        
        return unread_chats
    except Exception as e:
        logging.error(f"查找未读聊天出错: {e}")
        return []

def click_chat_and_verify(x, y):
    """点击聊天并验证是否成功"""
    try:
        logging.info(f"点击聊天位置: ({x}, {y})")
        pyautogui.click(x, y)
        time.sleep(1.5)  # 等待聊天窗口加载
        
        # 验证是否成功打开聊天窗口
        # 尝试获取聊天内容来验证
        return verify_chat_opened()
        
    except Exception as e:
        logging.error(f"点击聊天出错: {e}")
        return False

def verify_chat_opened():
    """验证聊天窗口是否已打开"""
    try:
        # 尝试选择聊天内容
        pyautogui.hotkey('ctrl', 'a')
        time.sleep(0.5)
        pyautogui.hotkey('ctrl', 'c')
        time.sleep(0.5)
        
        content = pyperclip.paste()
        
        if content and len(content.strip()) > 0:
            logging.info("聊天窗口已成功打开")
            return True
        else:
            logging.warning("聊天窗口可能未正确打开")
            return False
            
    except Exception as e:
        logging.error(f"验证聊天窗口时出错: {e}")
        return False

def get_latest_message():
    """获取最新消息"""
    try:
        # 获取剪贴板内容（之前已经复制过）
        content = pyperclip.paste()
        
        if content:
            lines = content.split('\n')
            messages = []
            
            for line in lines:
                line = line.strip()
                if line and len(line) < 200 and not line.startswith('['):
                    # 过滤掉时间戳和系统消息
                    if not line.startswith('——') and not line.endswith('撤回了一条消息'):
                        messages.append(line)
            
            if messages:
                latest_message = messages[-1]
                logging.info(f"获取到最新消息: {latest_message}")
                return latest_message
        
        return None
    except Exception as e:
        logging.error(f"获取消息出错: {e}")
        return None

def get_auto_reply(message):
    """根据消息内容生成自动回复"""
    message = message.strip().lower()
    
    if '你好' in message or 'hello' in message:
        return '你好，这条消息是通过autoreply程序进行自动回复！'
    elif '123' in message:
        return '你发送了123'
    elif '测试' in message or 'test' in message:
        return '测试成功'
    elif 'help' in message or '帮助' in message:
        return '可用命令：你好、123、测试'
    elif '?' in message or '？' in message:
        return '有什么可以帮助您的吗？'
    else:
        return '自动回复：收到您的消息了~'

def send_reply(message):
    """发送回复消息"""
    try:
        logging.info(f'准备发送回复: {message}')
        
        # 将回复复制到剪贴板
        pyperclip.copy(message)
        time.sleep(0.3)
        
        # 粘贴到输入框
        pyautogui.hotkey('ctrl', 'v')
        time.sleep(0.5)
        
        # 发送消息
        pyautogui.press('enter')
        time.sleep(0.5)
        
        logging.info('回复已发送')
        return True
    except Exception as e:
        logging.error(f'发送回复出错: {e}')
        return False

def main():
    """主函数"""
    logging.info('=== 微信精确点击自动回复程序启动 ===')
    logging.info(f'当前点击偏移设置: X偏移={CLICK_OFFSET_X}, Y偏移={CLICK_OFFSET_Y}')
    logging.info('如需调整点击位置，请修改代码中的 CLICK_OFFSET_X 和 CLICK_OFFSET_Y 参数')
    
    # 获取微信窗口
    hwnd = getHwnd()
    if hwnd is None:
        logging.error('无法找到微信窗口，程序退出')
        return

    try:
        # 连接微信窗口
        wx = WindowControl(Name="微信")
        if not wx.Exists(0, 0):
            logging.error('无法连接到微信窗口控件')
            return
            
        wx.SwitchToThisWindow()
        logging.info('已切换到微信窗口')
        
        # 确保在微信聊天界面
        try:
            wechat_button = wx.ButtonControl(Name="微信", searchDepth=6)
            if wechat_button.Exists(0, 0):
                wechat_button.Click()
                logging.info('已点击微信按钮，切换到聊天界面')
                time.sleep(1)
        except Exception as e:
            logging.warning(f'点击微信按钮时出错: {e}')

        logging.info('开始精确监听未读消息...')
        
        processed_positions = set()  # 记录已处理的位置
        
        while True:
            try:
                # 查找未读聊天
                unread_chats = find_unread_chats_precise(wx)
                
                for chat_x, chat_y in unread_chats:
                    position_key = f"{chat_x}_{chat_y}"
                    
                    if position_key not in processed_positions:
                        logging.info(f'发现新的未读聊天，准备点击位置: ({chat_x}, {chat_y})')
                        
                        # 点击聊天并验证
                        if click_chat_and_verify(chat_x, chat_y):
                            # 获取最新消息
                            latest_message = get_latest_message()
                            
                            if latest_message:
                                # 生成并发送回复
                                response = get_auto_reply(latest_message)
                                
                                if send_reply(response):
                                    processed_positions.add(position_key)
                                    logging.info('处理完成')
                            else:
                                logging.info('未能获取到有效消息')
                        else:
                            logging.warning('点击位置可能不准确，请调整 CLICK_OFFSET_X 参数')
                
                # 清理处理记录
                if len(processed_positions) > 50:
                    processed_positions.clear()
                
                time.sleep(3)  # 检查间隔
                
            except KeyboardInterrupt:
                logging.info('用户中断程序')
                break
            except Exception as e:
                logging.error(f"监听过程中出错: {e}")
                time.sleep(5)
                
    except Exception as e:
        logging.error(f"程序运行时出错: {e}")
    
    logging.info('=== 微信精确点击自动回复程序结束 ===')

if __name__ == "__main__":
    main()

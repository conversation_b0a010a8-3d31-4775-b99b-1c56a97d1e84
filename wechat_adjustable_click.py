import pyautogui
import win32gui
import win32con
import time
import logging
import pyperclip
from uiautomation import WindowControl
import threading
import keyboard

# 设置日志记录
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 可调整的参数
CLICK_OFFSET_X = 150  # 点击位置相对于聊天列表左边的偏移量
CLICK_OFFSET_Y = 0    # 点击位置相对于检测到的Y坐标的偏移量

def getHwnd():
    """获取微信窗口句柄"""
    try:
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_text = win32gui.GetWindowText(hwnd)
                if window_text == '微信':
                    windows.append(hwnd)
            return True
        
        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)
        
        if windows:
            hwnd = windows[0]
            try:
                win32gui.ShowWindow(hwnd, win32con.SW_SHOWNORMAL)
                time.sleep(0.5)
                win32gui.SetForegroundWindow(hwnd)
                logging.info("微信窗口已激活")
            except Exception as e:
                logging.warning(f"激活窗口警告: {e}")
            return hwnd
        else:
            logging.error("未找到微信窗口")
            return None
    except Exception as e:
        logging.error(f"获取窗口句柄出错: {e}")
        return None

def adjust_click_position():
    """监听键盘输入来调整点击位置"""
    global CLICK_OFFSET_X, CLICK_OFFSET_Y
    
    logging.info("键盘控制说明:")
    logging.info("按 'A' 键: X坐标左移10像素")
    logging.info("按 'D' 键: X坐标右移10像素") 
    logging.info("按 'W' 键: Y坐标上移10像素")
    logging.info("按 'S' 键: Y坐标下移10像素")
    logging.info("按 'Q' 键: 显示当前设置")
    logging.info("按 'ESC' 键: 退出程序")
    
    while True:
        try:
            if keyboard.is_pressed('a'):
                CLICK_OFFSET_X -= 10
                logging.info(f"X坐标左移，当前偏移: X={CLICK_OFFSET_X}, Y={CLICK_OFFSET_Y}")
                time.sleep(0.3)
            elif keyboard.is_pressed('d'):
                CLICK_OFFSET_X += 10
                logging.info(f"X坐标右移，当前偏移: X={CLICK_OFFSET_X}, Y={CLICK_OFFSET_Y}")
                time.sleep(0.3)
            elif keyboard.is_pressed('w'):
                CLICK_OFFSET_Y -= 10
                logging.info(f"Y坐标上移，当前偏移: X={CLICK_OFFSET_X}, Y={CLICK_OFFSET_Y}")
                time.sleep(0.3)
            elif keyboard.is_pressed('s'):
                CLICK_OFFSET_Y += 10
                logging.info(f"Y坐标下移，当前偏移: X={CLICK_OFFSET_X}, Y={CLICK_OFFSET_Y}")
                time.sleep(0.3)
            elif keyboard.is_pressed('q'):
                logging.info(f"当前点击偏移设置: X={CLICK_OFFSET_X}, Y={CLICK_OFFSET_Y}")
                time.sleep(0.3)
            elif keyboard.is_pressed('esc'):
                logging.info("用户按ESC键退出程序")
                break
            
            time.sleep(0.1)
        except:
            break

def find_unread_chats_precise(wx):
    """精确查找未读聊天"""
    try:
        rect = wx.BoundingRectangle
        left, top, right, bottom = rect.left, rect.top, rect.right, rect.bottom
        
        chat_list_left = left + 20
        chat_list_right = left + (right - left) // 3
        chat_list_top = top + 120
        chat_list_bottom = bottom - 100
        
        unread_chats = []
        
        # 在聊天列表区域查找红色圆点
        for y in range(chat_list_top, chat_list_bottom, 60):
            for x in range(chat_list_right - 80, chat_list_right - 10, 10):
                try:
                    pixel_color = pyautogui.pixel(x, y)
                    r, g, b = pixel_color
                    if r > 180 and g < 80 and b < 80:
                        chat_y = y + CLICK_OFFSET_Y
                        chat_x = chat_list_left + CLICK_OFFSET_X
                        
                        unread_chats.append((chat_x, chat_y))
                        logging.info(f"发现未读聊天标识在位置: ({x}, {y})")
                        logging.info(f"计算的点击位置: ({chat_x}, {chat_y})")
                        break
                except:
                    pass
        
        return unread_chats
    except Exception as e:
        logging.error(f"查找未读聊天出错: {e}")
        return []

def test_click_position(wx):
    """测试点击位置功能"""
    logging.info("=== 测试点击位置功能 ===")
    logging.info("将在5秒后进行测试点击...")
    time.sleep(5)
    
    try:
        rect = wx.BoundingRectangle
        left, top, right, bottom = rect.left, rect.top, rect.right, rect.bottom
        
        chat_list_left = left + 20
        chat_list_top = top + 120
        
        # 计算测试点击位置
        test_x = chat_list_left + CLICK_OFFSET_X
        test_y = chat_list_top + 50  # 第一个聊天项的位置
        
        logging.info(f"测试点击位置: ({test_x}, {test_y})")
        pyautogui.click(test_x, test_y)
        
        logging.info("测试点击完成，请检查是否点击到了正确的聊天项")
        
    except Exception as e:
        logging.error(f"测试点击时出错: {e}")

def main():
    """主函数"""
    logging.info('=== 微信可调整点击位置自动回复程序启动 ===')
    logging.info(f'初始点击偏移设置: X偏移={CLICK_OFFSET_X}, Y偏移={CLICK_OFFSET_Y}')
    
    # 获取微信窗口
    hwnd = getHwnd()
    if hwnd is None:
        logging.error('无法找到微信窗口，程序退出')
        return

    try:
        # 连接微信窗口
        wx = WindowControl(Name="微信")
        if not wx.Exists(0, 0):
            logging.error('无法连接到微信窗口控件')
            return
            
        wx.SwitchToThisWindow()
        logging.info('已切换到微信窗口')
        
        # 启动键盘监听线程
        keyboard_thread = threading.Thread(target=adjust_click_position, daemon=True)
        keyboard_thread.start()
        
        # 询问是否进行测试点击
        logging.info("是否需要测试点击位置？程序将在10秒后自动开始监听...")
        time.sleep(3)
        
        # 进行测试点击
        test_click_position(wx)
        
        logging.info('开始监听未读消息...')
        logging.info('您可以使用 A/D/W/S 键实时调整点击位置')
        
        processed_positions = set()
        
        while True:
            try:
                # 查找未读聊天
                unread_chats = find_unread_chats_precise(wx)
                
                for chat_x, chat_y in unread_chats:
                    position_key = f"{chat_x}_{chat_y}"
                    
                    if position_key not in processed_positions:
                        logging.info(f'发现新的未读聊天，点击位置: ({chat_x}, {chat_y})')
                        
                        # 点击聊天
                        pyautogui.click(chat_x, chat_y)
                        time.sleep(1.5)
                        
                        # 尝试获取消息并回复
                        try:
                            pyautogui.hotkey('ctrl', 'a')
                            time.sleep(0.5)
                            pyautogui.hotkey('ctrl', 'c')
                            time.sleep(0.5)
                            
                            content = pyperclip.paste()
                            if content:
                                lines = content.split('\n')
                                messages = [line.strip() for line in lines if line.strip() and len(line.strip()) < 200]
                                
                                if messages:
                                    latest_message = messages[-1]
                                    logging.info(f"收到消息: {latest_message}")
                                    
                                    # 生成回复
                                    if '你好' in latest_message:
                                        reply = '你好，这是自动回复！'
                                    elif '测试' in latest_message:
                                        reply = '测试成功'
                                    else:
                                        reply = '自动回复：已收到您的消息'
                                    
                                    # 发送回复
                                    pyperclip.copy(reply)
                                    pyautogui.hotkey('ctrl', 'v')
                                    time.sleep(0.3)
                                    pyautogui.press('enter')
                                    
                                    logging.info(f"已回复: {reply}")
                                    processed_positions.add(position_key)
                        except Exception as e:
                            logging.error(f"处理消息时出错: {e}")
                
                time.sleep(3)
                
            except KeyboardInterrupt:
                logging.info('用户中断程序')
                break
            except Exception as e:
                logging.error(f"监听过程中出错: {e}")
                time.sleep(5)
                
    except Exception as e:
        logging.error(f"程序运行时出错: {e}")
    
    logging.info('=== 程序结束 ===')

if __name__ == "__main__":
    main()

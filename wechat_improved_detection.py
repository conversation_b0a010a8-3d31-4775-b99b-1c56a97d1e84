import pyautogui
import win32gui
import win32con
import time
import logging
import pyperclip
from uiautomation import WindowControl

# 设置日志记录
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def getHwnd():
    """获取微信窗口句柄"""
    try:
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_text = win32gui.GetWindowText(hwnd)
                if window_text == '微信':
                    windows.append(hwnd)
            return True
        
        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)
        
        if windows:
            hwnd = windows[0]
            try:
                win32gui.ShowWindow(hwnd, win32con.SW_SHOWNORMAL)
                time.sleep(0.5)
                win32gui.SetForegroundWindow(hwnd)
                logging.info("微信窗口已激活")
            except Exception as e:
                logging.warning(f"激活窗口警告: {e}")
            return hwnd
        else:
            logging.error("未找到微信窗口")
            return None
    except Exception as e:
        logging.error(f"获取窗口句柄出错: {e}")
        return None

def is_unread_color(r, g, b):
    """判断是否是未读标识的颜色"""
    # 基于调试结果的颜色范围
    # 红色范围1: RGB(204, 78, 55) 类型
    if (180 <= r <= 220 and 60 <= g <= 100 and 40 <= b <= 70):
        return True
    
    # 红色范围2: RGB(255, 97, 70) 类型  
    if (240 <= r <= 255 and 80 <= g <= 120 and 60 <= b <= 90):
        return True
    
    # 橙色范围: RGB(249, 161, 70) 类型
    if (230 <= r <= 255 and 140 <= g <= 180 and 60 <= b <= 90):
        return True
    
    return False

def find_unread_chats_improved(wx):
    """改进的未读聊天检测"""
    try:
        rect = wx.BoundingRectangle
        left, top, right, bottom = rect.left, rect.top, rect.right, rect.bottom
        
        # 聊天列表区域
        chat_list_left = left + 20
        chat_list_right = left + (right - left) // 3
        chat_list_top = top + 120
        chat_list_bottom = bottom - 100
        
        logging.info(f"扫描区域: 左={chat_list_left}, 右={chat_list_right}, 上={chat_list_top}, 下={chat_list_bottom}")
        
        unread_chats = []
        
        # 更精确的扫描：每个聊天项大约60像素高
        for row in range(0, 15):  # 扫描前15个聊天项
            chat_y = chat_list_top + (row * 60) + 30  # 聊天项中心
            
            if chat_y > chat_list_bottom:
                break
            
            # 在聊天项的右侧区域查找未读标识
            for x in range(chat_list_right - 80, chat_list_right - 10, 5):
                try:
                    pixel_color = pyautogui.pixel(x, chat_y)
                    r, g, b = pixel_color
                    
                    if is_unread_color(r, g, b):
                        # 计算点击位置
                        click_x = chat_list_left + 150  # 聊天项的点击位置
                        click_y = chat_y
                        
                        unread_chats.append((click_x, click_y))
                        logging.info(f"发现未读聊天! 标识位置:({x}, {chat_y}), RGB({r}, {g}, {b})")
                        logging.info(f"计算点击位置: ({click_x}, {click_y})")
                        break  # 找到一个就跳出内层循环
                        
                except Exception as e:
                    pass
        
        return unread_chats
        
    except Exception as e:
        logging.error(f"查找未读聊天出错: {e}")
        return []

def click_chat_and_get_message(x, y):
    """点击聊天并获取消息"""
    try:
        logging.info(f"点击聊天位置: ({x}, {y})")
        pyautogui.click(x, y)
        time.sleep(2)  # 等待聊天窗口加载
        
        # 获取聊天内容
        pyautogui.hotkey('ctrl', 'a')
        time.sleep(0.5)
        pyautogui.hotkey('ctrl', 'c')
        time.sleep(0.5)
        
        content = pyperclip.paste()
        
        if content:
            lines = content.split('\n')
            messages = []
            
            for line in lines:
                line = line.strip()
                if (line and len(line) < 200 and 
                    not line.startswith('[') and 
                    not line.startswith('——') and
                    not line.endswith('撤回了一条消息')):
                    messages.append(line)
            
            if messages:
                latest_message = messages[-1]
                logging.info(f"获取到最新消息: {latest_message}")
                return latest_message
        
        logging.warning("未能获取到有效消息")
        return None
        
    except Exception as e:
        logging.error(f"点击聊天或获取消息时出错: {e}")
        return None

def get_auto_reply(message):
    """生成自动回复"""
    message = message.lower().strip()
    
    if '你好' in message or 'hello' in message:
        return '你好！这是自动回复程序的回复。'
    elif '测试' in message or 'test' in message:
        return '测试成功！程序运行正常。'
    elif '123' in message:
        return '收到数字123'
    elif '?' in message or '？' in message:
        return '有什么可以帮助您的吗？'
    elif '谢谢' in message or 'thank' in message:
        return '不客气！'
    else:
        return '自动回复：已收到您的消息，谢谢！'

def send_reply(message):
    """发送回复"""
    try:
        logging.info(f"准备发送回复: {message}")
        
        # 复制回复到剪贴板
        pyperclip.copy(message)
        time.sleep(0.3)
        
        # 粘贴并发送
        pyautogui.hotkey('ctrl', 'v')
        time.sleep(0.5)
        pyautogui.press('enter')
        time.sleep(0.3)
        
        logging.info("回复已发送")
        return True
        
    except Exception as e:
        logging.error(f"发送回复时出错: {e}")
        return False

def main():
    """主函数"""
    logging.info('=== 微信改进版自动回复程序启动 ===')
    
    # 获取微信窗口
    hwnd = getHwnd()
    if hwnd is None:
        logging.error('无法找到微信窗口，程序退出')
        return

    try:
        # 连接微信窗口
        wx = WindowControl(Name="微信")
        if not wx.Exists(0, 0):
            logging.error('无法连接到微信窗口控件')
            return
            
        wx.SwitchToThisWindow()
        logging.info('已切换到微信窗口')
        
        # 确保在微信聊天界面
        try:
            wechat_button = wx.ButtonControl(Name="微信", searchDepth=6)
            if wechat_button.Exists(0, 0):
                wechat_button.Click()
                logging.info('已点击微信按钮，切换到聊天界面')
                time.sleep(2)
        except Exception as e:
            logging.warning(f'点击微信按钮时出错: {e}')

        logging.info('开始改进版未读消息监听...')
        logging.info('程序将使用调试得到的精确颜色范围检测未读消息')
        
        processed_positions = set()  # 记录已处理的位置
        
        while True:
            try:
                # 查找未读聊天
                unread_chats = find_unread_chats_improved(wx)
                
                if unread_chats:
                    logging.info(f"发现 {len(unread_chats)} 个未读聊天")
                
                for chat_x, chat_y in unread_chats:
                    position_key = f"{chat_x}_{chat_y}"
                    
                    if position_key not in processed_positions:
                        # 点击聊天并获取消息
                        latest_message = click_chat_and_get_message(chat_x, chat_y)
                        
                        if latest_message:
                            # 生成并发送回复
                            response = get_auto_reply(latest_message)
                            
                            if send_reply(response):
                                processed_positions.add(position_key)
                                logging.info(f"成功处理未读消息: {latest_message[:50]}...")
                        else:
                            logging.warning("未能获取到消息内容")
                
                # 清理处理记录
                if len(processed_positions) > 50:
                    processed_positions.clear()
                
                time.sleep(5)  # 检查间隔
                
            except KeyboardInterrupt:
                logging.info('用户中断程序')
                break
            except Exception as e:
                logging.error(f"监听过程中出错: {e}")
                time.sleep(5)
                
    except Exception as e:
        logging.error(f"程序运行时出错: {e}")
    
    logging.info('=== 微信改进版自动回复程序结束 ===')

if __name__ == "__main__":
    main()
